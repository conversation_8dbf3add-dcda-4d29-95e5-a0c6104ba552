# ====================
# Application Settings
# ====================
APP_NAME="Electricity Bill Management"
APP_ENV=local
APP_KEY=base64:tqdUI4y5ZHZ12mvhhclqY+Zpycac5e0dD5apvP2kvtc=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

# =============
# Localization
# =============
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

# ==================
# Database Settings
# ==================
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=electricity_billing
DB_USERNAME=billing_app
DB_PASSWORD=654321 # <-- Set actual password here

# SQLite Fallback
SQLITE_DATABASE=database/database.sqlite

# ========================
# Session & Cache Settings
# ========================
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false

CACHE_DRIVER=file
QUEUE_CONNECTION=database

# =============
# Redis
# =============
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# =============
# Mail Settings
# =============
MAIL_MAILER=log
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# ===================
# Security Settings
# ===================
BCRYPT_ROUNDS=12
SANCTUM_STATEFUL_DOMAINS=localhost:8000

# =============
# Frontend
# =============
VITE_APP_NAME="${APP_NAME}"
