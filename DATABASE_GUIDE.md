# Database Management Guide - SQLite & MySQL

## 🗄️ Current Setup

The system is configured to support both **SQLite** (development) and **MySQL** (production) databases with easy switching between them.

## 📋 Database Configuration

### Current Connections:
- **SQLite**: `database/database.sqlite` (default)
- **MySQL**: `electricity_billing` database on localhost

### Environment Variables:
```env
# Default connection
DB_CONNECTION=sqlite

# MySQL Configuration
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_DATABASE=electricity_billing
MYSQL_USERNAME=root
MYSQL_PASSWORD=

# SQLite Configuration
SQLITE_DATABASE=database/database.sqlite
```

## 🚀 Quick Start

### 1. Check Database Status
```bash
cd c:\last_app\bill
php artisan db:manage status
```

### 2. Set Up MySQL Database
```bash
# Option 1: Using MySQL Command Line
mysql -u root -p < c:\last_app\setup_mysql.sql

# Option 2: Using phpMyAdmin or MySQL Workbench
# Import the setup_mysql.sql file
```

### 3. Migrate Both Databases
```bash
# Migrate SQLite (current)
php artisan db:manage migrate --connection=sqlite

# Migrate MySQL
php artisan db:manage migrate --connection=mysql

# Or migrate both
php artisan db:manage migrate --connection=both
```

### 4. Seed Both Databases
```bash
# Seed SQLite
php artisan db:manage seed --connection=sqlite

# Seed MySQL
php artisan db:manage seed --connection=mysql

# Or seed both
php artisan db:manage seed --connection=both
```

## 🔄 Database Management Commands

### Status and Information
```bash
# Check connection status
php artisan db:manage status

# Show database statistics
php artisan db:manage stats --connection=sqlite
php artisan db:manage stats --connection=mysql
php artisan db:manage stats --connection=both
```

### Switching Databases
```bash
# Switch to MySQL as default
php artisan db:manage switch --connection=mysql

# Switch to SQLite as default
php artisan db:manage switch --connection=sqlite
```

### Data Synchronization
```bash
# Sync data from SQLite to MySQL
php artisan db:manage sync
# (Will prompt you to choose source and destination)
```

### Backup and Restore
```bash
# Backup SQLite database
php artisan db:manage backup --connection=sqlite

# Backup MySQL (manual process)
php artisan db:manage backup --connection=mysql
```

## 📊 Accessing Your Data

### 1. Using Laravel Tinker
```bash
cd c:\last_app\bill
php artisan tinker

# Access SQLite data
User::on('sqlite')->count()
Zone::on('sqlite')->with('collector')->get()

# Access MySQL data
User::on('mysql')->count()
Zone::on('mysql')->with('collector')->get()

# Current default database
User::count()
```

### 2. Using Database Commands
```bash
# Show all users in SQLite
php artisan tinker --execute="User::on('sqlite')->get()->each(function(\$u) { echo \$u->username . ' (' . \$u->role . ')' . PHP_EOL; })"

# Show all users in MySQL
php artisan tinker --execute="User::on('mysql')->get()->each(function(\$u) { echo \$u->username . ' (' . \$u->role . ')' . PHP_EOL; })"
```

### 3. Direct Database Access

#### SQLite
```bash
# Using SQLite command line
sqlite3 c:\last_app\bill\database\database.sqlite

# Common SQLite commands
.tables                    # Show all tables
.schema users             # Show table structure
SELECT * FROM users;      # Show all users
.quit                     # Exit
```

#### MySQL
```bash
# Using MySQL command line
mysql -u root -p electricity_billing

# Common MySQL commands
SHOW TABLES;              # Show all tables
DESCRIBE users;           # Show table structure
SELECT * FROM users;      # Show all users
EXIT;                     # Exit
```

## 🔧 Advanced Usage

### Using Specific Connections in Code

```php
// In your controllers or models
use Illuminate\Support\Facades\DB;

// Query specific database
$sqliteUsers = DB::connection('sqlite')->table('users')->get();
$mysqlUsers = DB::connection('mysql')->table('users')->get();

// Use models with specific connection
$users = User::on('mysql')->where('role', 'admin')->get();
$zones = Zone::on('sqlite')->with('subscribers')->get();
```

### Environment-Based Database Selection

```php
// In your models, you can set default connection based on environment
class User extends Authenticatable
{
    protected $connection; // Will use default from config
    
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        
        // Use MySQL in production, SQLite in development
        if (app()->environment('production')) {
            $this->connection = 'mysql';
        } else {
            $this->connection = 'sqlite';
        }
    }
}
```

## 📈 Database Statistics

### Current Data (as seeded):
- **Users**: 5 (1 admin, 2 collectors, 2 reviewers)
- **Zones**: 3 zones
- **Subscribers**: 5 subscribers
- **Readings**: ~9 readings (various statuses)
- **Bills**: ~5 bills (paid/unpaid)

### Sample Users:
| Username | Password | Role |
|----------|----------|------|
| admin | password | admin |
| collector1 | password | collector |
| collector2 | password | collector |
| reviewer1 | password | reviewer |
| reviewer2 | password | reviewer |

## 🛠️ Troubleshooting

### MySQL Connection Issues
```bash
# Check if MySQL is running
net start mysql

# Test connection
php artisan db:manage status
```

### SQLite Issues
```bash
# Check if SQLite file exists
ls -la c:\last_app\bill\database\database.sqlite

# Recreate if missing
php artisan migrate:fresh --database=sqlite
php artisan db:seed --database=sqlite --class=SystemSeeder
```

### Permission Issues
```bash
# Make sure storage directory is writable
chmod -R 755 storage/
chmod -R 755 database/
```

## 🔄 Migration Between Databases

### From SQLite to MySQL
```bash
# 1. Ensure MySQL is set up
php artisan db:manage status

# 2. Migrate MySQL
php artisan db:manage migrate --connection=mysql

# 3. Sync data
php artisan db:manage sync
# Choose: sqlite -> mysql

# 4. Switch default connection
php artisan db:manage switch --connection=mysql
```

### From MySQL to SQLite
```bash
# 1. Ensure SQLite is set up
php artisan db:manage migrate --connection=sqlite

# 2. Sync data
php artisan db:manage sync
# Choose: mysql -> sqlite

# 3. Switch default connection
php artisan db:manage switch --connection=sqlite
```

## 📱 API Usage

The API will automatically use the default database connection. To use a specific database in your API:

```php
// In your controllers
class ReadingController extends Controller
{
    public function index(Request $request)
    {
        // Use default database
        $readings = Reading::with(['subscriber', 'collector'])->get();
        
        // Or specify database
        $readings = Reading::on('mysql')->with(['subscriber', 'collector'])->get();
        
        return response()->json($readings);
    }
}
```

## 🎯 Best Practices

1. **Development**: Use SQLite for fast local development
2. **Testing**: Use SQLite for automated tests
3. **Production**: Use MySQL for production deployment
4. **Backup**: Regular backups of both databases
5. **Sync**: Keep databases in sync during development

## 📞 Support

For database-related issues:
1. Check connection status: `php artisan db:manage status`
2. View logs: `tail -f storage/logs/laravel.log`
3. Test connections: `php artisan tinker`

---

Your electricity billing system now supports both SQLite and MySQL with easy management and switching capabilities!