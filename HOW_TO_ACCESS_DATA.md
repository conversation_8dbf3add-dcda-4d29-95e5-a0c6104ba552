# How to Access Your Data - SQLite & MySQL

## 🎯 Current Status

✅ **SQLite Database**: Working with sample data  
❌ **MySQL Database**: Not connected (needs setup)

## 📊 Current Data in SQLite

Your system currently has:
- **5 Users**: admin, collector1, collector2, reviewer1, reviewer2
- **3 Zones**: المنطقة الشمالية، المنطقة الجنوبية، المنطقة الشرقية
- **5 Subscribers**: أحمد محمد علي، فاطمة عبدالله، محمد سعد الدين، نورا أحمد، خالد عبدالعزيز
- **9 Readings**: Various statuses (approved, pending, rejected)
- **5 Bills**: Mix of paid and unpaid bills

## 🚀 Quick Commands to Access Data

### 1. View All Data
```bash
cd c:\last_app\bill
php artisan data:show all
```

### 2. View Specific Tables
```bash
# Users
php artisan data:show users

# Zones
php artisan data:show zones

# Subscribers
php artisan data:show subscribers

# Readings
php artisan data:show readings

# Bills
php artisan data:show bills
```

### 3. Database Statistics
```bash
# Current database stats
php artisan db:manage stats

# Specific database stats
php artisan db:manage stats --connection=sqlite
php artisan db:manage stats --connection=mysql
```

### 4. Database Status
```bash
# Check connection status
php artisan db:manage status
```

## 🗄️ Setting Up MySQL

### Step 1: Install MySQL
If you don't have MySQL installed:
1. Download from: https://dev.mysql.com/downloads/mysql/
2. Or use XAMPP: https://www.apachefriends.org/

### Step 2: Create Database
```sql
-- Option 1: Using MySQL Command Line
mysql -u root -p
CREATE DATABASE electricity_billing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;

-- Option 2: Use the provided script
mysql -u root -p < c:\last_app\setup_mysql.sql
```

### Step 3: Update MySQL Credentials
Edit `c:\last_app\bill\.env`:
```env
# Update these values with your MySQL credentials
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_DATABASE=electricity_billing
MYSQL_USERNAME=root
MYSQL_PASSWORD=your_mysql_password
```

### Step 4: Setup MySQL Database
```bash
cd c:\last_app\bill

# Test MySQL connection
php artisan db:manage status

# Migrate MySQL database
php artisan db:manage migrate --connection=mysql

# Seed MySQL with sample data
php artisan db:manage seed --connection=mysql
```

## 🔄 Working with Both Databases

### Switch Default Database
```bash
# Switch to MySQL
php artisan db:manage switch --connection=mysql

# Switch to SQLite
php artisan db:manage switch --connection=sqlite
```

### Sync Data Between Databases
```bash
# Copy data from SQLite to MySQL
php artisan db:manage sync
# Choose: sqlite -> mysql

# Copy data from MySQL to SQLite
php artisan db:manage sync
# Choose: mysql -> sqlite
```

### View Data from Specific Database
```bash
# View SQLite data
php artisan data:show users --connection=sqlite

# View MySQL data
php artisan data:show users --connection=mysql
```

## 💻 Direct Database Access

### SQLite Access
```bash
# Using SQLite command line
sqlite3 c:\last_app\bill\database\database.sqlite

# Common commands:
.tables                    # Show all tables
.schema users             # Show table structure
SELECT * FROM users;      # Show all users
SELECT * FROM zones;      # Show all zones
.quit                     # Exit
```

### MySQL Access
```bash
# Using MySQL command line
mysql -u root -p electricity_billing

# Common commands:
SHOW TABLES;              # Show all tables
DESCRIBE users;           # Show table structure
SELECT * FROM users;      # Show all users
SELECT * FROM zones;      # Show all zones
EXIT;                     # Exit
```

## 🔧 Using Laravel Tinker

### Access Current Database
```bash
cd c:\last_app\bill
php artisan tinker

# In tinker:
User::all()                           # All users
Zone::with('collector')->get()        # Zones with collectors
Subscriber::with('zone')->get()       # Subscribers with zones
Reading::with('subscriber')->get()    # Readings with subscribers
Bill::with('reading.subscriber')->get() # Bills with subscriber info
```

### Access Specific Database
```bash
php artisan tinker

# In tinker:
User::on('sqlite')->get()            # SQLite users
User::on('mysql')->get()             # MySQL users
Zone::on('sqlite')->with('collector')->get()  # SQLite zones
```

## 📱 API Access

Your Laravel API automatically uses the default database. Test with:

```bash
# Start Laravel server
php artisan serve

# Test API endpoints:
curl http://localhost:8000/api/login -X POST -H "Content-Type: application/json" -d '{"username":"admin","password":"password"}'

# Use the token to access protected endpoints:
curl http://localhost:8000/api/users -H "Authorization: Bearer YOUR_TOKEN"
```

## 🛠️ Database Tools

### GUI Tools for Database Management

#### SQLite:
- **DB Browser for SQLite**: https://sqlitebrowser.org/
- **SQLiteStudio**: https://sqlitestudio.pl/

#### MySQL:
- **phpMyAdmin**: Web-based (comes with XAMPP)
- **MySQL Workbench**: https://dev.mysql.com/downloads/workbench/
- **HeidiSQL**: https://www.heidisql.com/

### File Locations
- **SQLite Database**: `c:\last_app\bill\database\database.sqlite`
- **Laravel Config**: `c:\last_app\bill\config\database.php`
- **Environment**: `c:\last_app\bill\.env`

## 🔍 Sample Queries

### Get User Login Credentials
```sql
-- All users with their roles
SELECT username, role FROM users;

-- Collectors only
SELECT username FROM users WHERE role = 'collector';
```

### Get Zone Information
```sql
-- Zones with collector information
SELECT z.name as zone_name, u.username as collector 
FROM zones z 
LEFT JOIN users u ON z.collector_id = u.id;
```

### Get Reading Statistics
```sql
-- Reading counts by status
SELECT status, COUNT(*) as count 
FROM readings 
GROUP BY status;

-- Average consumption
SELECT AVG(current_reading - previous_reading) as avg_consumption 
FROM readings 
WHERE status = 'approved';
```

### Get Bill Information
```sql
-- Unpaid bills with subscriber info
SELECT s.name, s.subscription_no, b.amount, b.issued_at 
FROM bills b 
JOIN readings r ON b.reading_id = r.id 
JOIN subscribers s ON r.subscriber_id = s.id 
WHERE b.is_paid = 0;
```

## 🎯 Next Steps

1. **Set up MySQL** if you want production database
2. **Use the API** with your Flutter app
3. **Access data** using the commands above
4. **Sync databases** when needed
5. **Monitor** using the stats commands

## 📞 Quick Help

```bash
# Show all available commands
php artisan db:manage

# Show data commands
php artisan data:show

# Get help
php artisan help db:manage
php artisan help data:show
```

Your electricity billing system is ready with full data access capabilities! 🎉