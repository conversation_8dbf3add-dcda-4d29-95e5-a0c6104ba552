# 🔐 Login Guide - How to Access the System

## 👥 Available User Accounts

All users have the same password: **`password`**

| Username | Password | Role | Access Level |
|----------|----------|------|--------------|
| **admin** | password | Admin | Full system access |
| **collector1** | password | Collector | Zone 1 (المنطقة الشمالية) |
| **collector2** | password | Collector | Zone 2 (المنطقة الجنوبية) |
| **reviewer1** | password | Reviewer | Review all readings |
| **reviewer2** | password | Reviewer | Review all readings |

## 🚀 How to Login

### 1. **Flutter Mobile App**
```bash
# Start the Flutter app
cd c:\last_app\bill\frontend
flutter run
```

**Login Steps:**
1. Open the app
2. Enter username (e.g., `admin`)
3. Enter password: `password`
4. Tap "تسجيل الدخول" (Login)
5. You'll be redirected based on your role

### 2. **API Login (for testing)**
```bash
# Start Laravel server
cd c:\last_app\bill
php artisan serve

# Test login via API
curl -X POST http://localhost:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password"}'
```

**Expected Response:**
```json
{
  "access_token": "1|abc123...",
  "token_type": "Bearer",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "created_at": "2025-06-13T21:00:00.000000Z",
    "updated_at": "2025-06-13T21:00:00.000000Z"
  }
}
```

## 🎯 What Each User Can Do

### 👨‍💼 **Admin** (`admin` / `password`)
- **Dashboard**: View system statistics
- **User Management**: Add/edit/delete users
- **Zone Management**: Manage zones and assign collectors
- **Subscriber Management**: Manage all subscribers
- **Reading Review**: Review all readings
- **Bill Management**: Manage all bills and payments
- **Reports**: View system reports and analytics

### 👷‍♂️ **Collector1** (`collector1` / `password`)
- **Zone**: المنطقة الشمالية (Northern Zone)
- **Subscribers**: أحمد محمد علي، فاطمة عبدالله، خالد عبدالعزيز
- **Functions**:
  - View subscribers in their zone
  - Add new meter readings
  - Take photos of meters
  - Use OCR to extract readings
  - View their reading history

### 👷‍♂️ **Collector2** (`collector2` / `password`)
- **Zone**: المنطقة الجنوبية (Southern Zone)
- **Subscribers**: محمد سعد الدين، نورا أحمد
- **Functions**: Same as Collector1 but for their zone

### 🔍 **Reviewer1** (`reviewer1` / `password`)
- **Functions**:
  - Review pending readings
  - Approve or reject readings
  - Add review notes
  - View reading history
  - See reading images

### 🔍 **Reviewer2** (`reviewer2` / `password`)
- **Functions**: Same as Reviewer1

## 🖥️ **Testing Login from Command Line**

### Test All Users:
```bash
cd c:\last_app\bill

# Test admin login
curl -X POST http://localhost:8000/api/login -H "Content-Type: application/json" -d '{"username":"admin","password":"password"}'

# Test collector login
curl -X POST http://localhost:8000/api/login -H "Content-Type: application/json" -d '{"username":"collector1","password":"password"}'

# Test reviewer login
curl -X POST http://localhost:8000/api/login -H "Content-Type: application/json" -d '{"username":"reviewer1","password":"password"}'
```

## 📱 **Flutter App Login Flow**

1. **Start the app**: `flutter run`
2. **Login screen appears** with Arabic interface
3. **Enter credentials**:
   - Username: `admin` (or any other username)
   - Password: `password`
4. **App redirects** based on role:
   - **Admin** → Admin Dashboard
   - **Collector** → Collector Dashboard
   - **Reviewer** → Reviewer Dashboard

## 🔧 **Troubleshooting Login Issues**

### If Login Fails:
```bash
# Check if Laravel server is running
cd c:\last_app\bill
php artisan serve

# Check database connection
php artisan db:manage status

# Verify users exist
php artisan data:show users

# Check Laravel logs
tail -f storage/logs/laravel.log
```

### Common Issues:
1. **"Invalid credentials"**: Make sure username and password are correct
2. **"Connection refused"**: Laravel server not running
3. **"Database error"**: Database not accessible

### Reset Password (if needed):
```bash
cd c:\last_app\bill
php artisan tinker

# In tinker:
$user = App\Models\User::where('username', 'admin')->first();
$user->password = Hash::make('newpassword');
$user->save();
exit
```

## 🎮 **Quick Test Scenarios**

### Scenario 1: Admin Login
```
Username: admin
Password: password
Expected: Admin dashboard with full system overview
```

### Scenario 2: Collector Login
```
Username: collector1
Password: password
Expected: Collector dashboard showing Zone 1 subscribers
```

### Scenario 3: Reviewer Login
```
Username: reviewer1
Password: password
Expected: Reviewer dashboard showing pending readings
```

## 🔐 **Security Notes**

- **Development Only**: These are demo credentials
- **Production**: Change all passwords before deployment
- **Password Policy**: Implement strong password requirements
- **Token Expiry**: API tokens expire after session timeout

## 📊 **Current System Data**

After login, you'll see:
- **5 Users** (as shown above)
- **3 Zones** with Arabic names
- **5 Subscribers** with Arabic names
- **9 Readings** (2 pending, 5 approved, 2 rejected)
- **5 Bills** (2 paid, 3 unpaid)

## 🚀 **Next Steps After Login**

1. **Explore the interface** for your role
2. **Test functionality** (add readings, review, etc.)
3. **Check different user roles** by logging in with different accounts
4. **Use the API** with the provided token

---

**Remember**: All passwords are `password` for demo purposes! 🔑