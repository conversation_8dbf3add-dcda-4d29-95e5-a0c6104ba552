# 🔑 Quick Login Reference

## 📋 Login Credentials

| Username | Password | Role | What They Can Do |
|----------|----------|------|------------------|
| **admin** | password | Admin | Everything - manage users, zones, subscribers, bills |
| **collector1** | password | Collector | Add readings for Zone 1 (المنطقة الشمالية) |
| **collector2** | password | Collector | Add readings for Zone 2 (المنطقة الجنوبية) |
| **reviewer1** | password | Reviewer | Review and approve/reject readings |
| **reviewer2** | password | Reviewer | Review and approve/reject readings |

## 🚀 How to Start

### Option 1: Flutter Mobile App
```bash
cd c:\last_app\bill\frontend
flutter run
```
Then login with any username above and password: `password`

### Option 2: Laravel API
```bash
cd c:\last_app\bill
php artisan serve
```
API will be available at: `http://localhost:8000/api`

## 🎯 Quick Test

1. **Start Flutter app**: `flutter run`
2. **Login as admin**: username=`admin`, password=`password`
3. **Explore the dashboard** with full system access

## 📊 What You'll See

- **5 Users** ready to login
- **3 Zones** with Arabic names
- **5 Subscribers** with sample data
- **9 Readings** in various states
- **5 Bills** (some paid, some unpaid)

## 🔧 Troubleshooting

If login doesn't work:
```bash
cd c:\last_app\bill
php artisan db:manage status
php artisan data:show users
```

---
**Remember: All passwords are `password`** 🔐