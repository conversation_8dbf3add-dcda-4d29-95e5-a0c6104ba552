# نظام إدارة فواتير الكهرباء

نظام شامل لإدارة فواتير الكهرباء يتكون من:
- **Backend**: Laravel + Sanctum للمصادقة
- **OCR Service**: Python + Flask لاستخراج قراءات العدادات
- **Frontend**: Flutter للتطبيق المحمول
- **Database**: SQLite (يمكن تغييرها إلى MySQL)

## 🚀 التثبيت والتشغيل

### 1. Laravel Backend

```bash
cd bill
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate:fresh
php artisan db:seed --class=SystemSeeder
php artisan serve
```

سيعمل الـ API على: `http://localhost:8000`

### 2. Python OCR Service

```bash
cd ocr_service
pip install -r requirements.txt
python app.py
```

ستعمل خدمة OCR على: `http://localhost:5000`

### 3. Flutter Frontend

```bash
cd bill/frontend
flutter pub get
flutter run
```

## 👥 المستخدمين التجريبيين

| الدور | اسم المستخدم | كلمة المرور |
|-------|-------------|------------|
| مدير | admin | password |
| محصل 1 | collector1 | password |
| محصل 2 | collector2 | password |
| مراجع 1 | reviewer1 | password |
| مراجع 2 | reviewer2 | password |

## 📱 ميزات التطبيق

### للمدير (Admin)
- لوحة تحكم شاملة مع الإحصائيات
- إدارة المناطق والمحصلين
- إدارة المشتركين
- مراجعة جميع القراءات والفواتير
- تقارير مالية ومتابعة الأداء

### للمحصل (Collector)
- عرض قائمة المشتركين في منطقته
- إضافة قراءات جديدة
- التقاط صور العدادات
- استخراج تلقائي للقراءات باستخدام OCR
- متابعة حالة القراءات المرسلة

### للمراجع (Reviewer)
- مراجعة القراءات المعلقة
- عرض صور العدادات
- اعتماد أو رفض القراءات
- إضافة ملاحظات عند الرفض
- متابعة القراءات المراجعة

## 🔧 API Endpoints

### المصادقة
- `POST /api/login` - تسجيل الدخول
- `POST /api/logout` - تسجيل الخروج
- `GET /api/user` - بيانات المستخدم الحالي

### ال��ناطق
- `GET /api/zones` - قائمة المناطق
- `POST /api/zones` - إضافة منطقة جديدة
- `PUT /api/zones/{id}` - تحديث منطقة
- `DELETE /api/zones/{id}` - حذف منطقة

### المشتركين
- `GET /api/subscribers` - قائمة المشتركين
- `POST /api/subscribers` - إضافة مشترك جديد
- `PUT /api/subscribers/{id}` - تحديث مشترك
- `DELETE /api/subscribers/{id}` - حذف مشترك

### القراءات
- `GET /api/readings` - قائمة القراءات
- `POST /api/readings` - إضافة قراءة جديدة
- `GET /api/readings-pending` - القراءات المعلقة
- `PUT /api/readings/{id}/approve` - اعتماد قراءة
- `PUT /api/readings/{id}/reject` - رفض قراءة

### الفواتير
- `GET /api/bills` - قائمة الفواتير
- `POST /api/bills/generate` - توليد فواتير تلقائياً
- `PUT /api/bills/{id}/mark-paid` - تحديد فاتورة كمدفوعة
- `GET /api/bills-overdue` - الفواتير المتأخرة

## 🤖 خدمة OCR

### استخراج قراءة العداد
```bash
POST /ocr
Content-Type: multipart/form-data

image: [ملف الصورة]
```

### الاستجابة
```json
{
  "success": true,
  "subscriber_no": "12345",
  "current_reading": 1340.5,
  "confidence": "high"
}
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية
1. `users` - المستخدمين (مدير، محصل، مراجع)
2. `zones` - المناطق
3. `subscribers` - المشتركين
4. `readings` - القراءات
5. `bills` - الفواتير
6. `personal_access_tokens` - توكنات المصادقة

## 📋 المتطلبات

### Laravel
- PHP >= 8.1
- Composer
- SQLite أو MySQL

### Python OCR
- Python >= 3.8
- OpenCV
- EasyOCR
- Flask

### Flutter
- Flutter SDK >= 3.0
- Dart >= 3.0

## 🔒 الأمان

- مصادقة باستخدام Laravel Sanctum
- تشفير كلمات المرور
- التحقق من الأذونات حسب الدور
- حماية API endpoints
- رفع آمن للصور

## 📈 التطوير المستقبلي

- [ ] إضافة إشعارات push
- [ ] تقارير متقدمة وتحليلات
- [ ] دعم عدة لغات
- [ ] تطبيق ويب للإدارة
- [ ] تكامل مع أنظمة الدفع
- [ ] نسخ احتياطية تلقائية

## 🆘 الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.

---

تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والأمان والسهولة في الاستخدام.