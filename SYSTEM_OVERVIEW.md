# نظام إدارة فواتير الكهرباء - نظرة عامة

## 🎯 الهدف من النظام
نظام شامل لإدارة فواتير الكهرباء يهدف إلى:
- تسهيل عملية جمع قراءات العدادات
- أتمتة عملية استخراج القراءات من الصور
- تبسيط عملية مراجعة واعتماد القراءات
- إنشاء الفواتير تلقائياً
- متابعة حالة الدفع والمتأخرات

## 🏗️ معمارية النظام

### 1. Backend (Laravel + Sanctum)
- **التقنيات**: Laravel 12, PHP 8.4, SQLite, Sanctum
- **الوظائف**:
  - إدارة المستخدمين والمصادقة
  - إدارة المناطق والمشتركين
  - معالجة القراءات والفواتير
  - API RESTful للتطبيق المحمول

### 2. OCR Service (Python + Flask)
- **المسار**: 'electricity_bill\ocr_service'
- **التقنيات**: Python, Flask, OpenCV, EasyOCR
- **الوظائف**:
  - استخراج الأرقام من صور العدادات
  - تحسين جودة الصور
  - التعرف على النصوص العربية والإنجليزية

### 3. Frontend (Flutter)
- **المسار**: `elelctricity_bill\frontend`
- **التقنيات**: Flutter, Dart
- **الوظائف**:
  - واجهات مستخدم مختلفة حسب الدور
  - التقاط ورفع الصور
  - عرض البيانات والإحصائيات

## 👥 أدوار المستخدمين

### المدير (Admin)
- **اسم المستخدم**:
- **كلمة المرور**: 
  - عرض لوحة التحكم الشاملة
  - إدارة المناطق والمحصلين
  - إدارة المشتركين
  - مراجعة جميع القراءات
  - إدارة الفواتير والتقارير المالية

### المحصل (Collector)
- **أسماء المستخدمين**: 
- **كلمة المرور**: 
- **الصلاحيات**:
  - عرض المشتركين في منطقته فقط
  - إضافة قراءات ��ديدة
  - التقاط صور العدادات
  - استخدام OCR لاستخراج القراءات
  - متابعة حالة قراءاته

### المراجع (Reviewer)
- **أسماء المستخدمين**: 
- **كلمة المرور**: 
- **الصلاحيات**:
  - مراجعة القراءات المعلقة
  - عرض صور العدادات
  - اعتماد أو رفض القراءات
  - إضافة ملاحظات للقراءات المرفوضة

## 📊 البيانات التجريبية

### المناطق (3 مناطق)
1. المنطقة الشمالية - محصل: collector1
2. المنطقة الجنوبية - محصل: collector2
3. المنطقة الشرقية - بدون محصل

### المشتركين (5 مشتركين)
- أحمد محمد علي (رقم الاشتراك: 12345)
- فاطمة عبدالله (رقم الاشتراك: 12346)
- محمد سعد الدين (رقم الاشتراك: 12347)
- نورا أحمد (رقم الاشتراك: 12348)
- خالد عبدالعزيز (رقم الاشتراك: 12349)

### القراءات والفواتير
- 9 قراءات (معتمدة، معلقة، مرفوضة)
- 5 فواتير (مدفوعة وغير مدفوعة)

## 🔧 API Endpoints الرئيسية

### المصادقة
```
POST /api/register
POST /api/login
POST /api/logout
GET /api/user
```

### إدارة البيانات
```
GET|POST|PUT|DELETE /api/zones
GET|POST|PUT|DELETE /api/subscribers
GET|POST|PUT|DELETE /api/readings
GET|POST|PUT|DELETE /api/bills
```

### عمليات خاصة
```
GET /api/readings-pending
PUT /api/readings/{id}/approve
PUT /api/readings/{id}/reject
POST /api/bills/generate
PUT /api/bills/{id}/mark-paid
GET /api/bills-overdue
```

### OCR Service
```
POST /ocr (رفع صورة واستخراج القراءة)
GET /health (فحص حالة الخدمة)
```

## 🚀 تشغيل النظام

### 1. تشغيل Laravel API
```bash
cd c:\elelctricity_bill\bill
php artisan serve
```

### 2. تشغيل OCR Service
```bash
cd c:\elelctricity_bill\ocr_service
pip install -r requirements.txt
python app.py
```

### 3. تشغيل Flutter App
```bash
cd c:\elelctricity_bill\bill\frontend
flutter pub get
flutter run
```

## 📱 ميزات التطبيق المحمول

### واجهة المحصل
- قائمة المشتركين في منطقته
- نموذج إضافة قراءة جديدة
- التقاط صورة العداد
- استخراج تلقائي للقراءة باستخدام OCR
- متابعة حالة القراءات (معلقة، معتمدة، مرفوضة)

### واجهة المراجع
- قائمة القراءات المعلقة
- عرض تفاصيل كل قراءة
- عرض صورة العداد
- أزرار اعتماد/رفض
- إضافة ملاحظات عند الرفض
- تاريخ القراءات المراجعة

### واجهة المدير
- لوحة تحكم بالإحصائيات
- بطاقات عرض الأرقام الرئيسية
- إجراءات سريعة للإدارة
- تنبيهات للقراءات المعلقة والفواتير المتأخرة

## 🔒 الأمان والحماية

### مصادقة المستخدمين
- Laravel Sanctum للتوكنات
- تشفير كلمات المرور
- انتهاء صلاحية التوكنات

### التحكم في الوصول
- فلترة البيانات حسب دور المستخدم
- المحصل يرى مشتركي منطقته فقط
- المراجع يرى القراءات المعلقة والمراجعة
- المدير يرى جميع البيانات

### حماية البيانات
- التحقق من صحة البيانات المدخلة
- حماية من SQL Injection
- رفع آمن للصور
- تشفير الاتصالات

## 📈 الإحصائيات الحالية

- **المستخدمين**: 5 (1 مدير، 2 محصل، 2 مراجع)
- **المناطق**: 3 مناطق
- **المشتركين**: 5 مشتركين
- **القراءات**: 9 قراءات
- **الفو��تير**: 5 فواتير

## 🔮 التطوير المستقبلي

### المرحلة التالية
- [ ] إضافة واجهات إدارة المناطق والمشتركين
- [ ] تحسين خدمة OCR لدقة أعلى
- [ ] إضافة تقارير مفصلة
- [ ] نظام إشعارات

### المراحل المتقدمة
- [ ] تطبيق ويب للإدارة
- [ ] تكامل مع أنظمة الدفع
- [ ] تطبيق للعملاء لعرض فواتيرهم
- [ ] نظام تحليلات متقدم

## 🆘 استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في الاتصال بقاعدة البيانات**: تأكد من وجود ملف `database.sqlite`
2. **فشل في تسجيل الدخول**: تحقق من اسم المستخدم وكلمة المرور
3. **عدم عمل OCR**: تأكد من تثبيت المتطلبات Python
4. **مشاكل في Flutter**: تشغيل `flutter doctor` للتحقق

### ملفات السجلات
- Laravel: `storage/logs/laravel.log`
- OCR Service: عرض في Terminal
- Flutter: عرض في IDE أو Terminal

---

النظام جاهز للاستخدام والتطوير! 🎉

APP_NAME="Electricity Bill Management"
APP_ENV=local
APP_KEY=base64:tqdUI4y5ZHZ12mvhhclqY+Zpycac5e0dD5apvP2kvtc=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost:8000

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Default database connection (postgres for production, sqlite for local/offline)
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=electricity_billing
DB_USERNAME=billing_app
DB_PASSWORD=654321  # <-- billing_app's password

# SQLite Configuration (for local/offline development)
SQLITE_CONNECTION=sqlite
SQLITE_DATABASE=database/database.sqlite

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"
