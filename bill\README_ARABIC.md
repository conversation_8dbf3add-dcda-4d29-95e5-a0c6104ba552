# نظام إدارة الفواتير وقراءة العدادات للمشتركين

## 🎯 نظرة عامة

نظام شامل لإدارة المناطق، المشتركين، تسجيل قراءات العدادات، وإنشاء الفواتير تلقائياً. يتكون النظام من:
- **Backend**: Laravel 12 مع Sanctum للمصادقة
- **Frontend**: Flutter للواجهة المحمولة
- **Database**: SQLite (يمكن تغييرها إلى MySQL)

## 🚀 المميزات الرئيسية

### 📍 إدارة المناطق
- إنشاء وتعديل وحذف المناطق
- تحديد سعر الوحدة لكل منطقة
- عرض إحصائيات المشتركين في كل منطقة

### 👥 إدارة المشتركين
- ربط المشتركين بالمناطق
- إدارة معلومات المشتركين الشخصية
- تتبع أرقام العدادات

### 📊 إدارة القراءات
- تسجيل قراءات العدادات مع التواريخ
- نظام موافقة ورفض القراءات
- حساب الاستهلاك تلقائياً
- إمكانية إرفا�� صور للقراءات

### 💰 إدارة الفواتير
- إنشاء فواتير تلقائياً من القراءات المعتمدة
- حساب المبالغ حسب الاستهلاك وسعر المنطقة
- تتبع حالات الفواتير (معلقة، مدفوعة، متأخرة)
- إنشاء أرقام فواتير فريدة

## 🛠️ التقنيات المستخدمة

### Backend (Laravel)
- **Laravel 12**: إطار العمل الرئيسي
- **Sanctum**: نظام المصادقة والتوكن
- **SQLite**: قاعدة البيانات (افتراضي)
- **Eloquent ORM**: للتعامل مع قاعدة البيانات

### Frontend (Flutter)
- **Flutter**: إطار العمل للتطبيق المحمول
- **HTTP Package**: للتواصل مع API
- **SharedPreferences**: لحفظ التوكن محلياً

## 📋 متطلبات التشغيل

### Backend
- PHP 8.2 أو أحدث
- Composer
- Laravel 12

### Frontend
- Flutter SDK
- Dart SDK

## 🚀 طريقة التشغيل

### 1. تشغيل Backend (Laravel)

```bash
# الانتقال إلى مجلد المشروع
cd c:\last_app\bill

# تثبيت المكتبات
composer install

# إنشاء مفتاح التطبيق
php artisan key:generate

# تشغيل المايجريشن
php artisan migrate

# تشغيل ا��خادم
php artisan serve
```

### 2. تشغيل Frontend (Flutter)

```bash
# الانتقال إلى مجلد Flutter
cd c:\last_app\bill\frontend

# تثبيت المكتبات
flutter pub get

# تشغيل التطبيق
flutter run
```

## 🔗 نقاط النهاية API

### المصادقة
- `POST /api/login` - تسجيل الدخول
- `POST /api/register` - إنشاء حساب جديد
- `POST /api/logout` - تسجيل الخروج

### المناطق
- `GET /api/zones` - عرض جميع المناطق
- `POST /api/zones` - إنشاء منطقة جديدة
- `PUT /api/zones/{id}` - تحديث منطقة
- `DELETE /api/zones/{id}` - حذف منطقة

### المشتركين
- `GET /api/subscribers` - عرض جميع المشتركين
- `POST /api/subscribers` - إنشاء مشترك جديد
- `PUT /api/subscribers/{id}` - تحديث مشترك
- `DELETE /api/subscribers/{id}` - حذف مشترك

### القراءات
- `GET /api/readings` - عرض جميع القراءات
- `POST /api/readings` - تسجيل قراءة جديدة
- `GET /api/readings-pending` - القراءات المعلقة
- `PUT /api/readings/{id}/approve` - الموافقة على قراءة
- `PUT /api/readings/{id}/reject` - رفض قراءة

### الفواتير
- `GET /api/bills` - عرض جميع الفواتير
- `POST /api/bills/generate` - إنشاء فواتير تلقائياً
- `PUT /api/bills/{id}/mark-paid` - تحديد فاتورة كمدفوعة
- `GET /api/bills-overdue` - الفواتير المتأخرة

## 📊 هيكل قاعدة البيانات

### الجداول الرئيسية

#### users
- معلومات المستخدمين وتسجيل الدخول

#### zones
- بيانات المناطق وأسعار الوحدات

#### subscribers
- معلومات المشتركين مع ربطها بالمناطق

#### readings
- قراءات العدادات مع حالات الموافقة

#### bills
- الفواتير المرتبطة بالقراءات والمشتركين

## 🔧 الإعدادات

### إعداد قاعدة البيانات
يمكن تغيير إعدادات قاعدة البيانات في ملف `.env`:

```env
DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite
```

### إعداد CORS
تم إعداد CORS للسماح بالطلبات من تطبيق Flutter.

## 📱 واجهة المستخدم

### الشاشة الرئيسية
- إحصائيات سريعة للنظام
- القراءات المعلقة للموافقة
- الفواتير الحديثة
- إجراءات سريعة

### المميزات المتاحة
- واجهة باللغة العربية مع دعم RTL
- تصميم متجاوب
- إشعارات للعمليات
- نظام تنقل سهل

## 🔐 الأمان

- استخدام Laravel Sanctum للمصادقة
- حماية جميع المسارات الحساسة
- تشفير كلمات المرور
- التحقق من صحة البيانات

## 📈 التطوير المستقبلي

- إضافة تقارير مفصلة
- نظام إشعارات
- دعم عدة مستخدمين بصلاحيات مختلفة
- تصدير الفواتير PDF
- لوحة تحكم ويب

## 🤝 المساهمة

يمكن المساهمة في تطوير النظام من خلال:
1. إنشاء Issues للمشاكل أو الاقتراحات
2. إرسال Pull Requests للتحسينات
3. تحسين التوثيق

## 📞 الدعم

للحصول على الدعم أو الاستفسارات، يرجى إنشاء Issue في المستودع.

---

**ملاحظة**: هذا النظام تم تطويره وفقاً للخطة المحددة ويمكن تخصيصه حسب الاحتياجات المحددة.