# نظام الأدوار والصلاحيات - Role System

## نظرة عامة

تم تحديث نظام الأدوار في التطبيق لاستخدام **Enums** و **Constants** بدلاً من القيم المكتوبة يدوياً (hard-coded values). هذا يحسن من:

- **الأمان**: منع الأخطاء الإملائية
- **القابلية للصيانة**: تغيير مركزي للأدوار
- **الوضوح**: أسماء واضحة للأدوار والصلاحيات

## الأدوار المتاحة

### 1. المدير (Admin)
- **القيمة**: `admin`
- **الاسم العربي**: `مدير`
- **الصلاحيات**:
  - إدارة المناطق (إنشاء، تعديل، حذف)
  - إدارة المشتركين (إنشاء، تعديل، حذف)
  - رؤية جميع الفواتير
  - تحديد الفواتير كمدفوعة
  - حذف القراءات والفواتير

### 2. المحصل (Collector)
- **القيمة**: `collector`
- **الاسم العربي**: `محصل`
- **الصلاحيات**:
  - إنشاء قراءات جديدة
  - رؤية الفواتير في منطقته فقط
  - تحديد الفواتير كمدفوعة
  - رؤية المشتركين في منطقته

### 3. المراجع (Reviewer)
- **القيمة**: `reviewer`
- **الاسم العربي**: `مراجع`
- **الصلاحيات**:
  - مراجعة القراءات (اعتماد/رفض)
  - إنشاء الفواتير
  - رؤية جميع الفواتير
  - رؤية جميع المشتركين

## استخدام النظام

### في Laravel (Backend)

#### 1. استخدام الـ Enum

```php
use App\Enums\UserRole;

// التحقق من الدور
if ($user->getRoleEnum() === UserRole::ADMIN) {
    // المستخدم مدير
}

// التحقق من الصلاحيات
if ($user->getRoleEnum()?->canCreateBills()) {
    // يمكن إنشاء فواتير
}
```

#### 2. استخدام الـ Constants (للتوافق مع الكود القديم)

```php
use App\Models\User;

// التحقق من الدور
if ($user->role === User::ROLE_ADMIN) {
    // المستخدم مدير
}

// التحقق من عدة أدوار
if ($user->hasRole(User::ROLE_ADMIN, User::ROLE_REVIEWER)) {
    // المستخدم مدير أو مراجع
}
```

#### 3. في الـ Policies

```php
public function create(User $user)
{
    // استخدام الـ enum للتحقق من الصلاحيات
    return $user->getRoleEnum()?->canCreateBills() ?? false;
}

// أو استخدام الطرق المباشرة
public function update(User $user, Bill $bill)
{
    return $user->isAdmin();
}
```

### في Flutter (Frontend)

```dart
// في الـ API Service أو الـ UI
final roleNames = {
  'admin': 'مدير',
  'collector': 'محصل',
  'reviewer': 'مراجع',
};

// عرض اسم الدور
String getRoleName(String role) {
  return roleNames[role] ?? role;
}
```

## الملفات المحدثة

### Backend Files
- `app/Models/User.php` - إضافة constants وطرق مساعدة
- `app/Enums/UserRole.php` - **جديد** - تعريف الـ enum والصلاحيات
- `app/Policies/BillPolicy.php` - استخدام constants بدلاً من القيم المكتوبة
- `app/Policies/ReadingPolicy.php` - استخدام constants
- `app/Policies/SubscriberPolicy.php` - استخدام constants
- `app/Policies/ZonePolicy.php` - استخدام constants
- `app/Http/Controllers/AuthController.php` - استخدام enum values للتحقق

### Test Files
- `tests/Unit/UserRoleTest.php` - **جديد** - اختبارات للتأكد من صحة النظام

## مثال كامل

```php
// إنشاء مستخدم جديد
$user = User::create([
    'username' => 'admin',
    'password' => Hash::make('password'),
    'role' => UserRole::ADMIN->value, // أو User::ROLE_ADMIN
]);

// التحقق من الصلاحيات
if ($user->getRoleEnum()?->canManageZones()) {
    // يمكن إدارة المناطق
    Zone::create(['name' => 'منطقة جديدة']);
}

// في الـ Policy
class ZonePolicy
{
    public function create(User $user)
    {
        return $user->getRoleEnum()?->canManageZones() ?? false;
    }
}
```

## فوائد التحديث

1. **منع الأخطاء**: لا يمكن كتابة دور خاطئ
2. **التوثيق الذاتي**: الكود يوضح الصلاحيات المتاحة
3. **سهولة الصيانة**: تغيير مركزي للأدوار
4. **الاختبار**: يمكن اختبار الصلاحيات بسهولة
5. **IDE Support**: اقتراحات أفضل في محرر الكود

## ملاحظات مهمة

- الـ Constants محفوظة للتوافق مع الكود القديم
- يُفضل استخدام الـ Enum في الكود الجديد
- جميع الـ Policies محدثة لاستخدام النظام الجديد
- الاختبارات تضمن عمل النظام بشكل صحيح
