<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use App\Models\User;
use App\Models\Zone;
use App\Models\Subscriber;
use App\Models\Reading;
use App\Models\Bill;

class DatabaseManager extends Command
{
    protected $signature = 'db:manage {action} {--connection=} {--force}';
    protected $description = 'Manage multiple database connections (SQLite and MySQL)';

    public function handle()
    {
        $action = $this->argument('action');
        $connection = $this->option('connection');

        switch ($action) {
            case 'status':
                $this->showStatus();
                break;
            case 'switch':
                $this->switchDatabase($connection);
                break;
            case 'migrate':
                $this->migrateDatabase($connection);
                break;
            case 'seed':
                $this->seedDatabase($connection);
                break;
            case 'sync':
                $this->syncDatabases();
                break;
            case 'backup':
                $this->backupDatabase($connection);
                break;
            case 'restore':
                $this->restoreDatabase($connection);
                break;
            case 'stats':
                $this->showStats($connection);
                break;
            default:
                $this->showHelp();
        }
    }

    private function showStatus()
    {
        $this->info('=== Database Status ===');
        
        // Current default connection
        $current = config('database.default');
        $this->line("Current default connection: <fg=green>{$current}</>");
        
        // Test SQLite connection
        try {
            DB::connection('sqlite')->getPdo();
            $this->line("SQLite: <fg=green>✓ Connected</>");
            $sqliteFile = database_path('database.sqlite');
            $this->line("  File: {$sqliteFile}");
            $this->line("  Size: " . $this->formatBytes(filesize($sqliteFile)));
        } catch (\Exception $e) {
            $this->line("SQLite: <fg=red>✗ Failed</>");
            $this->line("  Error: " . $e->getMessage());
        }
        
        // Test MySQL connection
        try {
            DB::connection('mysql')->getPdo();
            $this->line("MySQL: <fg=green>✓ Connected</>");
            $host = config('database.connections.mysql.host');
            $database = config('database.connections.mysql.database');
            $this->line("  Host: {$host}");
            $this->line("  Database: {$database}");
        } catch (\Exception $e) {
            $this->line("MySQL: <fg=red>✗ Failed</>");
            $this->line("  Error: " . $e->getMessage());
        }
    }

    private function switchDatabase($connection)
    {
        if (!$connection) {
            $connection = $this->choice('Select database connection:', ['sqlite', 'mysql']);
        }

        if (!in_array($connection, ['sqlite', 'mysql'])) {
            $this->error('Invalid connection. Use: sqlite or mysql');
            return;
        }

        // Update .env file
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);
        $envContent = preg_replace('/^DB_CONNECTION=.*/m', "DB_CONNECTION={$connection}", $envContent);
        file_put_contents($envFile, $envContent);

        $this->info("Switched to {$connection} database");
        $this->line("Please restart your application to apply changes.");
    }

    private function migrateDatabase($connection)
    {
        if (!$connection) {
            $connection = $this->choice('Select database to migrate:', ['sqlite', 'mysql', 'both']);
        }

        if ($connection === 'both') {
            $this->migrateConnection('sqlite');
            $this->migrateConnection('mysql');
        } else {
            $this->migrateConnection($connection);
        }
    }

    private function migrateConnection($connection)
    {
        $this->info("Migrating {$connection} database...");
        
        try {
            Artisan::call('migrate', [
                '--database' => $connection,
                '--force' => true
            ]);
            $this->line("✓ {$connection} migration completed");
        } catch (\Exception $e) {
            $this->error("✗ {$connection} migration failed: " . $e->getMessage());
        }
    }

    private function seedDatabase($connection)
    {
        if (!$connection) {
            $connection = $this->choice('Select database to seed:', ['sqlite', 'mysql', 'both']);
        }

        if ($connection === 'both') {
            $this->seedConnection('sqlite');
            $this->seedConnection('mysql');
        } else {
            $this->seedConnection($connection);
        }
    }

    private function seedConnection($connection)
    {
        $this->info("Seeding {$connection} database...");
        
        try {
            Artisan::call('db:seed', [
                '--database' => $connection,
                '--class' => 'SystemSeeder',
                '--force' => true
            ]);
            $this->line("✓ {$connection} seeding completed");
        } catch (\Exception $e) {
            $this->error("✗ {$connection} seeding failed: " . $e->getMessage());
        }
    }

    private function syncDatabases()
    {
        $from = $this->choice('Sync FROM which database?', ['sqlite', 'mysql']);
        $to = $from === 'sqlite' ? 'mysql' : 'sqlite';
        
        if (!$this->confirm("This will overwrite all data in {$to}. Continue?")) {
            return;
        }

        $this->info("Syncing data from {$from} to {$to}...");

        try {
            // Get data from source
            $users = User::on($from)->get();
            $zones = Zone::on($from)->with('collector')->get();
            $subscribers = Subscriber::on($from)->get();
            $readings = Reading::on($from)->get();
            $bills = Bill::on($from)->get();

            // Clear destination
            DB::connection($to)->table('bills')->delete();
            DB::connection($to)->table('readings')->delete();
            DB::connection($to)->table('subscribers')->delete();
            DB::connection($to)->table('zones')->delete();
            DB::connection($to)->table('users')->delete();

            // Insert data to destination
            foreach ($users as $user) {
                DB::connection($to)->table('users')->insert($user->toArray());
            }

            foreach ($zones as $zone) {
                DB::connection($to)->table('zones')->insert($zone->toArray());
            }

            foreach ($subscribers as $subscriber) {
                DB::connection($to)->table('subscribers')->insert($subscriber->toArray());
            }

            foreach ($readings as $reading) {
                DB::connection($to)->table('readings')->insert($reading->toArray());
            }

            foreach ($bills as $bill) {
                DB::connection($to)->table('bills')->insert($bill->toArray());
            }

            $this->info("✓ Data sync completed successfully");
        } catch (\Exception $e) {
            $this->error("✗ Sync failed: " . $e->getMessage());
        }
    }

    private function showStats($connection)
    {
        if (!$connection) {
            $connection = $this->choice('Select database for stats:', ['sqlite', 'mysql', 'both']);
        }

        if ($connection === 'both') {
            $this->showConnectionStats('sqlite');
            $this->line('');
            $this->showConnectionStats('mysql');
        } else {
            $this->showConnectionStats($connection);
        }
    }

    private function showConnectionStats($connection)
    {
        $this->info("=== {$connection} Database Statistics ===");
        
        try {
            $users = User::on($connection)->count();
            $zones = Zone::on($connection)->count();
            $subscribers = Subscriber::on($connection)->count();
            $readings = Reading::on($connection)->count();
            $bills = Bill::on($connection)->count();
            
            $pendingReadings = Reading::on($connection)->where('status', 'pending')->count();
            $approvedReadings = Reading::on($connection)->where('status', 'approved')->count();
            $rejectedReadings = Reading::on($connection)->where('status', 'rejected')->count();
            
            $paidBills = Bill::on($connection)->where('is_paid', true)->count();
            $unpaidBills = Bill::on($connection)->where('is_paid', false)->count();

            $this->table(
                ['Table', 'Count'],
                [
                    ['Users', $users],
                    ['Zones', $zones],
                    ['Subscribers', $subscribers],
                    ['Readings', $readings],
                    ['Bills', $bills],
                ]
            );

            $this->table(
                ['Reading Status', 'Count'],
                [
                    ['Pending', $pendingReadings],
                    ['Approved', $approvedReadings],
                    ['Rejected', $rejectedReadings],
                ]
            );

            $this->table(
                ['Bill Status', 'Count'],
                [
                    ['Paid', $paidBills],
                    ['Unpaid', $unpaidBills],
                ]
            );

        } catch (\Exception $e) {
            $this->error("Failed to get stats: " . $e->getMessage());
        }
    }

    private function backupDatabase($connection)
    {
        if (!$connection) {
            $connection = $this->choice('Select database to backup:', ['sqlite', 'mysql']);
        }

        $timestamp = now()->format('Y-m-d_H-i-s');
        $backupPath = storage_path("backups/{$connection}_{$timestamp}.sql");

        if (!is_dir(dirname($backupPath))) {
            mkdir(dirname($backupPath), 0755, true);
        }

        if ($connection === 'sqlite') {
            $sqliteFile = database_path('database.sqlite');
            $backupFile = storage_path("backups/sqlite_{$timestamp}.sqlite");
            copy($sqliteFile, $backupFile);
            $this->info("SQLite backup created: {$backupFile}");
        } else {
            // MySQL backup would require mysqldump
            $this->info("MySQL backup feature requires mysqldump. Please use your preferred MySQL backup tool.");
        }
    }

    private function showHelp()
    {
        $this->info('Database Manager Commands:');
        $this->line('');
        $this->line('  <fg=green>php artisan db:manage status</> - Show database connection status');
        $this->line('  <fg=green>php artisan db:manage switch --connection=mysql</> - Switch default database');
        $this->line('  <fg=green>php artisan db:manage migrate --connection=mysql</> - Run migrations');
        $this->line('  <fg=green>php artisan db:manage seed --connection=mysql</> - Seed database');
        $this->line('  <fg=green>php artisan db:manage sync</> - Sync data between databases');
        $this->line('  <fg=green>php artisan db:manage stats --connection=mysql</> - Show database statistics');
        $this->line('  <fg=green>php artisan db:manage backup --connection=sqlite</> - Backup database');
        $this->line('');
        $this->line('Options:');
        $this->line('  <fg=yellow>--connection=</> - Specify database connection (sqlite/mysql)');
        $this->line('  <fg=yellow>--force</> - Force operation without confirmation');
    }

    private function formatBytes($size, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, $precision) . ' ' . $units[$i];
    }
}