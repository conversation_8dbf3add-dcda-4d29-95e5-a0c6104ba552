<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Zone;
use App\Models\Subscriber;
use App\Models\Reading;
use App\Models\Bill;

class ShowData extends Command
{
    protected $signature = 'data:show {table?} {--connection=}';
    protected $description = 'Show data from database tables';

    public function handle()
    {
        $table = $this->argument('table');
        $connection = $this->option('connection') ?: config('database.default');

        if (!$table) {
            $table = $this->choice('Which table data to show?', [
                'users', 'zones', 'subscribers', 'readings', 'bills', 'all'
            ]);
        }

        $this->info("Showing data from {$connection} database:");
        $this->line('');

        switch ($table) {
            case 'users':
                $this->showUsers($connection);
                break;
            case 'zones':
                $this->showZones($connection);
                break;
            case 'subscribers':
                $this->showSubscribers($connection);
                break;
            case 'readings':
                $this->showReadings($connection);
                break;
            case 'bills':
                $this->showBills($connection);
                break;
            case 'all':
                $this->showUsers($connection);
                $this->line('');
                $this->showZones($connection);
                $this->line('');
                $this->showSubscribers($connection);
                $this->line('');
                $this->showReadings($connection);
                $this->line('');
                $this->showBills($connection);
                break;
        }
    }

    private function showUsers($connection)
    {
        $this->info('=== USERS ===');
        $users = User::on($connection)->get();
        
        $data = $users->map(function ($user) {
            return [
                'ID' => $user->id,
                'Username' => $user->username,
                'Role' => $user->role,
                'Created' => $user->created_at->format('Y-m-d H:i'),
            ];
        })->toArray();

        $this->table(['ID', 'Username', 'Role', 'Created'], $data);
    }

    private function showZones($connection)
    {
        $this->info('=== ZONES ===');
        $zones = Zone::on($connection)->with('collector')->get();
        
        $data = $zones->map(function ($zone) {
            return [
                'ID' => $zone->id,
                'Name' => $zone->name,
                'Collector' => $zone->collector ? $zone->collector->username : 'None',
                'Created' => $zone->created_at->format('Y-m-d H:i'),
            ];
        })->toArray();

        $this->table(['ID', 'Name', 'Collector', 'Created'], $data);
    }

    private function showSubscribers($connection)
    {
        $this->info('=== SUBSCRIBERS ===');
        $subscribers = Subscriber::on($connection)->with('zone')->get();
        
        $data = $subscribers->map(function ($subscriber) {
            return [
                'ID' => $subscriber->id,
                'Name' => $subscriber->name,
                'Subscription No' => $subscriber->subscription_no,
                'Zone' => $subscriber->zone ? $subscriber->zone->name : 'None',
                'Address' => substr($subscriber->address, 0, 30) . '...',
            ];
        })->toArray();

        $this->table(['ID', 'Name', 'Subscription No', 'Zone', 'Address'], $data);
    }

    private function showReadings($connection)
    {
        $this->info('=== READINGS ===');
        $readings = Reading::on($connection)->with(['subscriber', 'collector'])->get();
        
        $data = $readings->map(function ($reading) {
            return [
                'ID' => $reading->id,
                'Subscriber' => $reading->subscriber ? $reading->subscriber->name : 'Unknown',
                'Previous' => $reading->previous_reading,
                'Current' => $reading->current_reading,
                'Consumption' => $reading->current_reading - $reading->previous_reading,
                'Status' => $reading->status,
                'Collector' => $reading->collector ? $reading->collector->username : 'Unknown',
            ];
        })->toArray();

        $this->table(['ID', 'Subscriber', 'Previous', 'Current', 'Consumption', 'Status', 'Collector'], $data);
    }

    private function showBills($connection)
    {
        $this->info('=== BILLS ===');
        $bills = Bill::on($connection)->with('reading.subscriber')->get();
        
        $data = $bills->map(function ($bill) {
            return [
                'ID' => $bill->id,
                'Subscriber' => $bill->reading && $bill->reading->subscriber ? $bill->reading->subscriber->name : 'Unknown',
                'Amount' => number_format($bill->amount, 2) . ' SAR',
                'Issued' => $bill->issued_at->format('Y-m-d'),
                'Status' => $bill->is_paid ? 'Paid' : 'Unpaid',
                'Paid At' => $bill->paid_at ? $bill->paid_at->format('Y-m-d') : 'Not paid',
            ];
        })->toArray();

        $this->table(['ID', 'Subscriber', 'Amount', 'Issued', 'Status', 'Paid At'], $data);
    }
}