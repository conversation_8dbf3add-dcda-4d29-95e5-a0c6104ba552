<?php

namespace App\Enums;

enum UserRole: string
{
    case ADMIN = 'admin';
    case COLLECTOR = 'collector';
    case REVIEWER = 'reviewer';

    /**
     * Get the Arabic name for the role
     */
    public function getArabicName(): string
    {
        return match($this) {
            self::ADMIN => 'مدير',
            self::COLLECTOR => 'محصل',
            self::REVIEWER => 'مراجع',
        };
    }

    /**
     * Get all role values as array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get role names mapping
     */
    public static function getArabicNames(): array
    {
        return [
            self::ADMIN->value => self::ADMIN->getArabicName(),
            self::COLLECTOR->value => self::COLLECTOR->getArabicName(),
            self::REVIEWER->value => self::REVIEWER->getArabicName(),
        ];
    }

    /**
     * Check if the role can create readings
     */
    public function canCreateReadings(): bool
    {
        return $this === self::COLLECTOR;
    }

    /**
     * Check if the role can review readings
     */
    public function canReviewReadings(): bool
    {
        return $this === self::REVIEWER;
    }

    /**
     * Check if the role has admin privileges
     */
    public function isAdmin(): bool
    {
        return $this === self::ADMIN;
    }

    /**
     * Check if the role can manage zones
     */
    public function canManageZones(): bool
    {
        return $this === self::ADMIN;
    }

    /**
     * Check if the role can manage subscribers
     */
    public function canManageSubscribers(): bool
    {
        return $this === self::ADMIN;
    }

    /**
     * Check if the role can view bills
     */
    public function canViewBills(): bool
    {
        return in_array($this, [self::ADMIN, self::REVIEWER, self::COLLECTOR]);
    }

    /**
     * Check if the role can create bills
     */
    public function canCreateBills(): bool
    {
        return $this === self::REVIEWER;
    }

    /**
     * Check if the role can mark bills as paid
     */
    public function canMarkBillsAsPaid(): bool
    {
        return in_array($this, [self::ADMIN, self::COLLECTOR]);
    }
}
