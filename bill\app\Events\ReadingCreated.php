<?php

namespace App\Events;

use App\Models\Reading;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReadingCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Reading $reading;

    /**
     * Create a new event instance.
     */
    public function __construct(Reading $reading)
    {
        $this->reading = $reading;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('readings'),
        ];
    }
}
