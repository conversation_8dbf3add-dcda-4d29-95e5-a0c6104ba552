<?php

namespace App\Events;

use App\Models\Reading;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReadingStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Reading $reading;
    public string $oldStatus;
    public string $newStatus;
    public User $reviewer;

    /**
     * Create a new event instance.
     */
    public function __construct(Reading $reading, string $oldStatus, string $newStatus, User $reviewer)
    {
        $this->reading = $reading;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
        $this->reviewer = $reviewer;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('readings'),
            new PrivateChannel('user.' . $this->reading->collector_id),
        ];
    }
}
