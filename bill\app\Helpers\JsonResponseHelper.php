<?php

namespace App\Helpers;

use Illuminate\Http\JsonResponse;

class JsonResponseHelper
{
    /**
     * Create a JSON response with proper UTF-8 encoding for Arabic text
     */
    public static function success($data = null, string $message = null, int $status = 200): JsonResponse
    {
        $response = [];
        
        if ($message) {
            $response['message'] = $message;
        }
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        return response()->json($response, $status, [], JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * Create an error JSON response with proper UTF-8 encoding
     */
    public static function error(string $message, int $status = 400, $errors = null): JsonResponse
    {
        $response = ['message' => $message];
        
        if ($errors) {
            $response['errors'] = $errors;
        }
        
        return response()->json($response, $status, [], JSON_UNESCAPED_UNICODE);
    }
}
