<?php

namespace App\Http\Controllers;

use App\Models\Bill;
use App\Models\Reading;
use Illuminate\Http\Request;

class BillController extends Controller
{
    // سعر الكيلوواط/ساعة بالريال اليمني (يمكن جعله قابل للتكوين)
    const PRICE_PER_KWH = 257;

    public function index(Request $request)
    {
        $query = Bill::with(['reading.subscriber']);

        // فلترة حسب حالة الدفع
        if ($request->has('is_paid')) {
            $query->where('is_paid', $request->boolean('is_paid'));
        }

        // فلترة الفواتير المتأخرة
        if ($request->has('overdue') && $request->boolean('overdue')) {
            $query->where('is_paid', false)
                  ->where('issued_at', '<', now()->subDays(30));
        }

        $bills = $query->latest()->get();
        return response()->json($bills);
    }

    public function store(Request $request)
    {
        $request->validate([
            'reading_id' => 'required|exists:readings,id',
        ]);

        $reading = Reading::find($request->reading_id);

        // التحقق من أن القراءة معتمدة
        if (!$reading->isApproved()) {
            return response()->json([
                'message' => 'لا يمكن إنشاء فاتورة لقراءة غير معتمدة'
            ], 422);
        }

        // التحقق من عدم وجود فاتورة مسبقة
        if ($reading->bill) {
            return response()->json([
                'message' => 'توجد فاتورة مسبقة لهذه القراءة'
            ], 422);
        }

        $consumption = $reading->consumption;
        $amount = $consumption * self::PRICE_PER_KWH;

        $bill = Bill::create([
            'reading_id' => $reading->id,
            'amount' => $amount,
            'issued_at' => now(),
        ]);

        $bill->load(['reading.subscriber']);
        return response()->json($bill, 201);
    }

    public function show(Bill $bill)
    {
        $bill->load(['reading.subscriber.zone', 'reading.collector']);
        return response()->json($bill);
    }

    public function update(Request $request, Bill $bill)
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
        ]);

        $bill->update($request->validated());
        $bill->load(['reading.subscriber']);

        return response()->json($bill);
    }

    public function destroy(Bill $bill)
    {
        if ($bill->isPaid()) {
            return response()->json([
                'message' => 'لا يمكن حذف فاتورة مدفوعة'
            ], 422);
        }

        $bill->delete();
        return response()->json(['message' => 'تم حذف الفاتورة بنجاح']);
    }

    // توليد فواتير تلقائياً للقراءات المعتمدة
    public function generateBills()
    {
        $approvedReadings = Reading::where('status', 'approved')
                                  ->doesntHave('bill')
                                  ->get();

        $generatedBills = [];

        foreach ($approvedReadings as $reading) {
            $consumption = $reading->consumption;
            $amount = $consumption * self::PRICE_PER_KWH;

            $bill = Bill::create([
                'reading_id' => $reading->id,
                'amount' => $amount,
                'issued_at' => now(),
            ]);

            $bill->load(['reading.subscriber']);
            $generatedBills[] = $bill;
        }

        return response()->json([
            'message' => 'تم توليد ' . count($generatedBills) . ' فاتورة',
            'bills' => $generatedBills
        ]);
    }

    // تحديد الفاتورة كمدفوعة
    public function markAsPaid(Bill $bill)
    {
        if ($bill->isPaid()) {
            return response()->json([
                'message' => 'الفاتورة مدفوعة مسبقاً'
            ], 422);
        }

        $bill->markAsPaid();
        $bill->load(['reading.subscriber']);

        return response()->json($bill);
    }

    // الفواتير المتأخرة
    public function overdue()
    {
        $overdueBills = Bill::with(['reading.subscriber'])
                           ->where('is_paid', false)
                           ->where('issued_at', '<', now()->subDays(30))
                           ->get();

        return response()->json($overdueBills);
    }
}
