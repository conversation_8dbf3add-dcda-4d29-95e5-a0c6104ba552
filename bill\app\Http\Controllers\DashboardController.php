<?php

namespace App\Http\Controllers;

use App\Services\ReadingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    protected ReadingService $readingService;

    public function __construct(ReadingService $readingService)
    {
        $this->readingService = $readingService;
    }

    /**
     * Get dashboard statistics
     */
    public function stats(Request $request): JsonResponse
    {
        $stats = $this->readingService->getDashboardStats($request->user());
        
        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get reading trends for charts
     */
    public function trends(Request $request): JsonResponse
    {
        $days = $request->integer('days', 30);
        $trends = $this->readingService->getReadingTrends($request->user(), $days);
        
        return response()->json([
            'success' => true,
            'data' => $trends
        ]);
    }

    /**
     * Get consumption analysis
     */
    public function consumption(Request $request): JsonResponse
    {
        $analysis = $this->readingService->getConsumptionAnalysis($request->user());
        
        return response()->json([
            'success' => true,
            'data' => $analysis
        ]);
    }

    /**
     * Get zone performance (admin only)
     */
    public function zonePerformance(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بعرض هذه البيانات'
            ], 403);
        }

        $performance = $this->readingService->getZonePerformance();
        
        return response()->json([
            'success' => true,
            'data' => $performance
        ]);
    }
}
