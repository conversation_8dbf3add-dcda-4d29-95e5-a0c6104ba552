<?php

namespace App\Http\Controllers;

use App\Models\Reading;
use App\Http\Requests\StoreReadingRequest;
use App\Http\Requests\UpdateReadingRequest;
use App\Http\Resources\ReadingResource;
use App\Events\ReadingCreated;
use App\Events\ReadingStatusChanged;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Storage;

class ReadingController extends Controller
{
    // عرض جميع القراءات مع الفلاتر المناسبة
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Reading::with(['subscriber', 'collector', 'reviewer']);

        // فلترة حسب الحالة
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب المنطقة
        if ($request->filled('zone_id')) {
            $query->byZone($request->zone_id);
        }

        // إذا كان المستخدم محصل، يعرض فقط قراءاته
        if ($request->user()->isCollector()) {
            $query->byCollector($request->user()->id);
        }

        // إذا كان المراجع، يعرض قراءاته التي راجعها أو القراءات المعلقة
        if ($request->user()->isReviewer()) {
            $query->where(function ($q) use ($request) {
                $q->where('reviewed_by', $request->user()->id)
                  ->orWhere('status', 'pending');
            });
        }

        $readings = $query->latest()->paginate(15);
        return ReadingResource::collection($readings);
    }

    // إضافة قراءة جديدة
    public function store(StoreReadingRequest $request): JsonResponse
    {
        $data = $request->validated();
        $data['collector_id'] = $request->user()->id;

        // حساب مبلغ الفاتورة
        $consumption = $data['current_reading'] - $data['previous_reading'];
        $data['bill_amount'] = $consumption * 257; // 257 ريال يمني لكل كيلوواط

        // تحديد حالة الفاتورة (افتراضياً غير مدفوعة)
        $data['bill_status'] = $request->input('bill_status', 'not_paid');

        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('readings', 'public');
            $data['image_path'] = $imagePath;
        }

        $reading = Reading::create($data);
        $reading->load(['subscriber', 'collector']);

        // Fire event for new reading
        ReadingCreated::dispatch($reading);

        return response()->json([
            'message' => 'تم إضافة القراءة بنجاح',
            'data' => new ReadingResource($reading)
        ], 201);
    }

    // عرض قراءة معينة
    public function show(Reading $reading): ReadingResource
    {
        $reading->load(['subscriber', 'collector', 'reviewer', 'bill']);
        return new ReadingResource($reading);
    }

    // تعديل قراءة (إذا كانت الحالة معلقة فقط)
    public function update(UpdateReadingRequest $request, Reading $reading): JsonResponse
    {
        $data = $request->validated();

        if ($request->hasFile('image')) {
            if ($reading->image_path) {
                Storage::disk('public')->delete($reading->image_path);
            }
            $imagePath = $request->file('image')->store('readings', 'public');
            $data['image_path'] = $imagePath;
        }

        $reading->update($data);
        $reading->load(['subscriber', 'collector']);

        return response()->json([
            'message' => 'تم تحديث القراءة بنجاح',
            'data' => new ReadingResource($reading)
        ]);
    }

    // حذف قراءة (إذا كانت الحالة معلقة فقط)
    public function destroy(Reading $reading)
    {
        if ($reading->status !== 'pending') {
            return response()->json([
                'message' => 'لا يمكن حذف قراءة تم مراجعتها'
            ], 422);
        }

        if ($reading->image_path) {
            Storage::disk('public')->delete($reading->image_path);
        }

        $reading->delete();
        return response()->json(['message' => 'تم حذف القراءة بنجاح']);
    }

    // عرض القراءات المعلقة فقط
    public function pending(): AnonymousResourceCollection
    {
        $readings = Reading::with(['subscriber', 'collector'])
                          ->pending()
                          ->latest()
                          ->paginate(15);
        return ReadingResource::collection($readings);
    }

    // اعتماد القراءة (تغيير الحالة إلى approved)
    public function approve(Request $request, Reading $reading): JsonResponse
    {
        if (!$request->user()->isReviewer() && !$request->user()->isAdmin()) {
            return response()->json([
                'message' => 'غير مصرح لك بمراجعة القراءات'
            ], 403);
        }

        $oldStatus = $reading->status;

        $reading->update([
            'status' => 'approved',
            'reviewed_by' => $request->user()->id,
            'review_note' => $request->review_note,
        ]);

        // Fire event for status change
        ReadingStatusChanged::dispatch($reading, $oldStatus, 'approved', $request->user());

        $reading->load(['subscriber', 'collector', 'reviewer']);

        return response()->json([
            'message' => 'تم اعتماد القراءة بنجاح',
            'data' => new ReadingResource($reading)
        ]);
    }

    // رفض القراءة (تغيير الحالة إلى rejected)
    public function reject(Request $request, Reading $reading): JsonResponse
    {
        if (!$request->user()->isReviewer() && !$request->user()->isAdmin()) {
            return response()->json([
                'message' => 'غير مصرح لك بمراجعة القراءات'
            ], 403);
        }

        $request->validate([
            'review_note' => 'required|string|max:500',
        ], [
            'review_note.required' => 'يجب إدخال سبب الرفض',
            'review_note.max' => 'سبب الرفض يجب أن يكون أقل من 500 حرف',
        ]);

        $oldStatus = $reading->status;

        $reading->update([
            'status' => 'rejected',
            'reviewed_by' => $request->user()->id,
            'review_note' => $request->review_note,
        ]);

        // Fire event for status change
        ReadingStatusChanged::dispatch($reading, $oldStatus, 'rejected', $request->user());

        $reading->load(['subscriber', 'collector', 'reviewer']);

        return response()->json([
            'message' => 'تم رفض القراءة',
            'data' => new ReadingResource($reading)
        ]);
    }
}
