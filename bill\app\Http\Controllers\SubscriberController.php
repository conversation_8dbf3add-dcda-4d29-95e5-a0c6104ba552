<?php

namespace App\Http\Controllers;

use App\Models\Subscriber;
use App\Models\Zone;
use App\Helpers\JsonResponseHelper;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SubscriberController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Subscriber::with(['zone', 'latestReading']);

            // فلترة حسب المنطقة
            if ($request->has('zone_id')) {
                $query->where('zone_id', $request->zone_id);
            }

            // فلترة حسب البحث
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('subscription_no', 'like', "%{$search}%")
                      ->orWhere('address', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            }

            // فلترة حسب دور المستخدم إذا كان محصل
            $user = $request->user();
            if ($user && $user->role === 'collector') {
                // المحصل يرى فقط مشتركي منطقته
                $collectorZones = Zone::where('collector_id', $user->id)->pluck('id');
                $query->whereIn('zone_id', $collectorZones);
            }

            $subscribers = $query->latest()->paginate(15);

            return JsonResponseHelper::success($subscribers, 'تم جلب المشتركين بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في جلب المشتركين: ' . $e->getMessage(), 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        // التحقق من صلاحية المدير
        if (request()->user()->role !== 'admin') {
            return JsonResponseHelper::error('غير مخول للوصول', 403);
        }

        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'subscription_no' => 'required|string|unique:subscribers,subscription_no',
                'address' => 'required|string|max:500',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'zone_id' => 'required|exists:zones,id',
                'meter_number' => 'nullable|string|max:100',
                'connection_date' => 'nullable|date',
                'notes' => 'nullable|string|max:1000',
            ], [
                'name.required' => 'اسم المشترك مطلوب',
                'subscription_no.required' => 'رقم الاشتراك مطلوب',
                'subscription_no.unique' => 'رقم الاشتراك مستخدم مسبقاً',
                'address.required' => 'العنوان مطلوب',
                'zone_id.required' => 'المنطقة مطلوبة',
                'zone_id.exists' => 'المنطقة المحددة غير موجودة',
                'email.email' => 'البريد الإلكتروني غير صحيح',
                'connection_date.date' => 'تاريخ التوصيل غير صحيح',
            ]);

            $subscriber = Subscriber::create([
                'name' => $request->name,
                'subscription_no' => $request->subscription_no,
                'address' => $request->address,
                'phone' => $request->phone,
                'email' => $request->email,
                'zone_id' => $request->zone_id,
                'meter_number' => $request->meter_number,
                'connection_date' => $request->connection_date,
                'notes' => $request->notes,
            ]);

            $subscriber->load(['zone', 'latestReading']);

            return JsonResponseHelper::success($subscriber, 'تم إنشاء المشترك بنجاح', 201);
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في إنشاء المشترك: ' . $e->getMessage(), 500);
        }
    }

    public function show(Subscriber $subscriber): JsonResponse
    {
        try {
            $subscriber->load(['zone', 'readings.collector', 'bills']);
            return JsonResponseHelper::success($subscriber, 'تم جلب بيانات المشترك بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في جلب بيانات المشترك: ' . $e->getMessage(), 500);
        }
    }

    public function update(Request $request, Subscriber $subscriber): JsonResponse
    {
        // التحقق من صلاحية المدير
        if (request()->user()->role !== 'admin') {
            return JsonResponseHelper::error('غير مخول للوصول', 403);
        }

        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'subscription_no' => 'required|string|unique:subscribers,subscription_no,' . $subscriber->id,
                'address' => 'required|string|max:500',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'zone_id' => 'required|exists:zones,id',
                'meter_number' => 'nullable|string|max:100',
                'connection_date' => 'nullable|date',
                'notes' => 'nullable|string|max:1000',
            ], [
                'name.required' => 'اسم المشترك مطلوب',
                'subscription_no.required' => 'رقم الاشتراك مطلوب',
                'subscription_no.unique' => 'رقم الاشتراك مستخدم مسبقاً',
                'address.required' => 'العنوان مطلوب',
                'zone_id.required' => 'المنطقة مطلوبة',
                'zone_id.exists' => 'المنطقة المحددة غير موجودة',
                'email.email' => 'البريد الإلكتروني غير صحيح',
                'connection_date.date' => 'تاريخ التوصيل غير صحيح',
            ]);

            $subscriber->update([
                'name' => $request->name,
                'subscription_no' => $request->subscription_no,
                'address' => $request->address,
                'phone' => $request->phone,
                'email' => $request->email,
                'zone_id' => $request->zone_id,
                'meter_number' => $request->meter_number,
                'connection_date' => $request->connection_date,
                'notes' => $request->notes,
            ]);

            $subscriber->load(['zone', 'latestReading']);

            return JsonResponseHelper::success($subscriber, 'تم تحديث المشترك بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في تحديث المشترك: ' . $e->getMessage(), 500);
        }
    }

    public function destroy(Subscriber $subscriber): JsonResponse
    {
        // التحقق من صلاحية المدير
        if (request()->user()->role !== 'admin') {
            return JsonResponseHelper::error('غير مخول للوصول', 403);
        }

        try {
            // التحقق من وجود قراءات أو فواتير للمشترك
            if ($subscriber->readings()->count() > 0) {
                return JsonResponseHelper::error('لا يمكن حذف المشترك لوجود قراءات مرتبطة به', 422);
            }

            if ($subscriber->bills()->count() > 0) {
                return JsonResponseHelper::error('لا يمكن حذف المشترك لوجود فواتير مرتبطة به', 422);
            }

            $subscriber->delete();
            return JsonResponseHelper::success(null, 'تم حذف المشترك بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في حذف المشترك: ' . $e->getMessage(), 500);
        }
    }
}
