<?php

namespace App\Http\Controllers;

use App\Models\Subscriber;
use Illuminate\Http\Request;

class SubscriberController extends Controller
{
    public function index(Request $request)
    {
        $query = Subscriber::with(['zone', 'latestReading']);

        // فلترة حسب المنطقة
        if ($request->has('zone_id')) {
            $query->where('zone_id', $request->zone_id);
        }

        // فلترة حسب دور المستخدم إذا كان محصل
        if ($request->user() && $request->user()->isCollector()) {
            $collectorZones = $request->user()->zones->pluck('id');
            $query->whereIn('zone_id', $collectorZones);
        }

        $subscribers = $query->get();

        return response()->json($subscribers);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'subscription_no' => 'required|string|unique:subscribers',
            'address' => 'required|string',
            'zone_id' => 'required|exists:zones,id',
        ]);

        $subscriber = Subscriber::create($validated);
        $subscriber->load(['zone', 'latestReading']);

        return response()->json($subscriber, 201);
    }

    public function show(Subscriber $subscriber)
    {
        $subscriber->load(['zone', 'readings.collector', 'bills']);
        return response()->json($subscriber);
    }

    public function update(Request $request, Subscriber $subscriber)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'subscription_no' => 'required|string|unique:subscribers,subscription_no,' . $subscriber->id,
            'address' => 'required|string',
            'zone_id' => 'required|exists:zones,id',
        ]);

        $subscriber->update($validated);
        $subscriber->load(['zone', 'latestReading']);

        return response()->json($subscriber);
    }

    public function destroy(Subscriber $subscriber)
    {
        $subscriber->delete();
        return response()->json(['message' => 'تم حذف المشترك بنجاح']);
    }
}
