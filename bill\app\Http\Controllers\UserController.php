<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Enums\UserRole;
use App\Http\Resources\UserResource;
use App\Helpers\JsonResponseHelper;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = User::query();

            // Filter by role if specified
            if ($request->filled('role')) {
                $query->where('role', $request->role);
            }

            // Filter by status if specified
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Search by name or username
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('username', 'like', "%{$search}%")
                      ->orWhere('user_id', 'like', "%{$search}%");
                });
            }

            $users = $query->latest()->paginate(15);

            return JsonResponseHelper::success(
                UserResource::collection($users),
                'تم جلب المستخدمين بنجاح'
            );
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في جلب المستخدمين: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'user_id' => 'nullable|string|unique:users,user_id',
                'username' => 'required|string|max:255|unique:users',
                'name' => 'required|string|max:255',
                'phone' => 'nullable|string|max:20',
                'password' => 'required|string|min:8',
                'role' => 'required|in:admin,collector,reviewer',
                'area' => 'nullable|string|max:255',
                'status' => 'nullable|string|in:active,inactive',
            ], [
                'user_id.unique' => 'رقم المستخدم مستخدم مسبقاً',
                'username.required' => 'اسم المستخدم مطلوب',
                'username.unique' => 'اسم المستخدم مستخدم مسبقاً',
                'name.required' => 'الاسم مطلوب',
                'password.required' => 'كلمة المرور مطلوبة',
                'password.min' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
                'role.required' => 'الدور مطلوب',
                'role.in' => 'الدور المحدد غير صحيح',
            ]);

            $user = User::create([
                'user_id' => $request->user_id,
                'username' => $request->username,
                'name' => $request->name,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'area' => $request->area,
                'status' => $request->status ?? 'active',
            ]);

            $userData = [
                'id' => $user->getAttribute('id'),
                'username' => $user->username,
                'name' => $user->name,
                'role' => $user->role,
                'status' => $user->status,
            ];

            return JsonResponseHelper::success($userData, 'تم إنشاء المستخدم بنجاح', 201);
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في إنشاء المستخدم: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified user
     */
    public function show(User $user): UserResource
    {
        return new UserResource($user);
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'user_id' => [
                'nullable',
                'string',
                Rule::unique('users', 'user_id')->ignore($user->getAttribute('id'))
            ],
            'username' => [
                'required',
                'string',
                'max:255',
                Rule::unique('users', 'username')->ignore($user->getAttribute('id'))
            ],
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8',
            'role' => 'required|in:admin,collector,reviewer',
            'area' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:active,inactive',
        ], [
            'user_id.unique' => 'رقم المستخدم مستخدم مسبقاً',
            'username.required' => 'اسم المستخدم مطلوب',
            'username.unique' => 'اسم المستخدم مستخدم مسبقاً',
            'name.required' => 'الاسم مطلوب',
            'password.min' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
            'role.required' => 'الدور مطلوب',
            'role.in' => 'الدور المحدد غير صحيح',
        ]);

        $updateData = [
            'user_id' => $request->user_id,
            'username' => $request->username,
            'name' => $request->name,
            'phone' => $request->phone,
            'role' => $request->role,
            'area' => $request->area,
            'status' => $request->status ?? $user->status,
        ];

        // Only update password if provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return JsonResponseHelper::success(new UserResource($user), 'تم تحديث المستخدم بنجاح');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): JsonResponse
    {
        // Prevent deleting the current user
        if ($user->getAttribute('id') === request()->user()->id) {
            return JsonResponseHelper::error('لا يمكن حذف المستخدم الحالي', 422);
        }

        $user->delete();

        return JsonResponseHelper::success(null, 'تم حذف المستخدم بنجاح');
    }

    /**
     * Get users by role
     */
    public function getByRole(Request $request, string $role): AnonymousResourceCollection
    {
        $request->validate([
            'role' => 'required|in:admin,collector,reviewer',
        ]);

        $users = User::where('role', $role)
                    ->where('status', 'active')
                    ->latest()
                    ->get();

        return UserResource::collection($users);
    }

    /**
     * Toggle user status
     */
    public function toggleStatus(User $user): JsonResponse
    {
        $newStatus = $user->status === 'active' ? 'inactive' : 'active';

        $user->update(['status' => $newStatus]);

        $message = $newStatus === 'active' ? 'تم تفعيل المستخدم' : 'تم إلغاء تفعيل المستخدم';
        return JsonResponseHelper::success(new UserResource($user), $message);
    }

    /**
     * Get collectors for zones
     */
    public function getCollectors(): AnonymousResourceCollection
    {
        $collectors = User::where('role', 'collector')
                         ->where('status', 'active')
                         ->latest()
                         ->get();

        return UserResource::collection($collectors);
    }
}
