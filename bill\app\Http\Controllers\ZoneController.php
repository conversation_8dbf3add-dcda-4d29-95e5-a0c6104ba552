<?php

namespace App\Http\Controllers;

use App\Models\Zone;
use App\Models\User;
use Illuminate\Http\Request;

class ZoneController extends Controller
{
    public function index()
    {
        $zones = Zone::with(['collector', 'subscribers'])->get();
        return response()->json($zones);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'collector_id' => 'nullable|exists:users,id',
        ]);

        // التحقق من أن المحصل له دور collector
        if ($request->collector_id) {
            $collector = User::find($request->collector_id);
            if (!$collector->isCollector()) {
                return response()->json([
                    'message' => 'المستخدم المحدد ليس محصلاً'
                ], 422);
            }
        }

        $zone = Zone::create($request->validated());
        $zone->load(['collector', 'subscribers']);

        return response()->json($zone, 201);
    }

    public function show(Zone $zone)
    {
        $zone->load(['collector', 'subscribers']);
        return response()->json($zone);
    }

    public function update(Request $request, Zone $zone)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'collector_id' => 'nullable|exists:users,id',
        ]);

        // التحقق من أن المحصل له دور collector
        if ($request->collector_id) {
            $collector = User::find($request->collector_id);
            if (!$collector->isCollector()) {
                return response()->json([
                    'message' => 'المستخدم المحدد ليس محصلاً'
                ], 422);
            }
        }

        $zone->update($request->validated());
        $zone->load(['collector', 'subscribers']);

        return response()->json($zone);
    }

    public function destroy(Zone $zone)
    {
        $zone->delete();
        return response()->json(['message' => 'تم حذف المنطقة بنجاح']);
    }

    // الحصول على المحصلين المتاحين
    public function availableCollectors()
    {
        $collectors = User::where('role', 'collector')->get();
        return response()->json($collectors);
    }
}