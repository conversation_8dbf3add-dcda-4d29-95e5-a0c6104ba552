<?php

namespace App\Http\Controllers;

use App\Models\Zone;
use App\Models\User;
use App\Helpers\JsonResponseHelper;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ZoneController extends Controller
{
    public function index(): JsonResponse
    {
        try {
            $zones = Zone::with(['collector', 'reviewer', 'subscribers'])->get();
            return JsonResponseHelper::success($zones, 'تم جلب المناطق بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في جلب المناطق: ' . $e->getMessage(), 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        // التحقق من صلاحية المدير
        if (request()->user()->role !== 'admin') {
            return JsonResponseHelper::error('غير مخول للوصول', 403);
        }

        try {
            $request->validate([
                'name' => 'required|string|max:255|unique:zones,name',
                'collector_id' => 'nullable|exists:users,id',
                'reviewer_id' => 'nullable|exists:users,id',
                'description' => 'nullable|string|max:500',
            ], [
                'name.required' => 'اسم المنطقة مطلوب',
                'name.unique' => 'اسم المنطقة مستخدم مسبقاً',
                'collector_id.exists' => 'المحصل المحدد غير موجود',
                'reviewer_id.exists' => 'المراجع المحدد غير موجود',
            ]);

            // التحقق من أن المحصل له دور collector
            if ($request->collector_id) {
                $collector = User::find($request->collector_id);
                if ($collector->role !== 'collector') {
                    return JsonResponseHelper::error('المستخدم المحدد ليس محصلاً', 422);
                }
            }

            // التحقق من أن المراجع له دور reviewer
            if ($request->reviewer_id) {
                $reviewer = User::find($request->reviewer_id);
                if ($reviewer->role !== 'reviewer') {
                    return JsonResponseHelper::error('المستخدم المحدد ليس مراجعاً', 422);
                }
            }

            $zone = Zone::create([
                'name' => $request->name,
                'collector_id' => $request->collector_id,
                'reviewer_id' => $request->reviewer_id,
                'description' => $request->description,
            ]);

            $zone->load(['collector', 'reviewer', 'subscribers']);

            return JsonResponseHelper::success($zone, 'تم إنشاء المنطقة بنجاح', 201);
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في إنشاء المنطقة: ' . $e->getMessage(), 500);
        }
    }

    public function show(Zone $zone): JsonResponse
    {
        try {
            $zone->load(['collector', 'reviewer', 'subscribers']);
            return JsonResponseHelper::success($zone, 'تم جلب بيانات المنطقة بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في جلب بيانات المنطقة: ' . $e->getMessage(), 500);
        }
    }

    public function update(Request $request, Zone $zone): JsonResponse
    {
        // التحقق من صلاحية المدير
        if (request()->user()->role !== 'admin') {
            return JsonResponseHelper::error('غير مخول للوصول', 403);
        }

        try {
            $request->validate([
                'name' => 'required|string|max:255|unique:zones,name,' . $zone->id,
                'collector_id' => 'nullable|exists:users,id',
                'reviewer_id' => 'nullable|exists:users,id',
                'description' => 'nullable|string|max:500',
            ], [
                'name.required' => 'اسم المنطقة مطلوب',
                'name.unique' => 'اسم المنطقة مستخدم مسبقاً',
                'collector_id.exists' => 'المحصل المحدد غير موجود',
                'reviewer_id.exists' => 'المراجع المحدد غير موجود',
            ]);

            // التحقق من أن المحصل له دور collector
            if ($request->collector_id) {
                $collector = User::find($request->collector_id);
                if ($collector->role !== 'collector') {
                    return JsonResponseHelper::error('المستخدم المحدد ليس محصلاً', 422);
                }
            }

            // التحقق من أن المراجع له دور reviewer
            if ($request->reviewer_id) {
                $reviewer = User::find($request->reviewer_id);
                if ($reviewer->role !== 'reviewer') {
                    return JsonResponseHelper::error('المستخدم المحدد ليس مراجعاً', 422);
                }
            }

            $zone->update([
                'name' => $request->name,
                'collector_id' => $request->collector_id,
                'reviewer_id' => $request->reviewer_id,
                'description' => $request->description,
            ]);

            $zone->load(['collector', 'reviewer', 'subscribers']);

            return JsonResponseHelper::success($zone, 'تم تحديث المنطقة بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في تحديث المنطقة: ' . $e->getMessage(), 500);
        }
    }

    public function destroy(Zone $zone): JsonResponse
    {
        // التحقق من صلاحية المدير
        if (request()->user()->role !== 'admin') {
            return JsonResponseHelper::error('غير مخول للوصول', 403);
        }

        try {
            // التحقق من وجود مشتركين في المنطقة
            if ($zone->subscribers()->count() > 0) {
                return JsonResponseHelper::error('لا يمكن حذف المنطقة لوجود مشتركين بها', 422);
            }

            $zone->delete();
            return JsonResponseHelper::success(null, 'تم حذف المنطقة بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في حذف المنطقة: ' . $e->getMessage(), 500);
        }
    }

    // الحصول على المحصلين المتاحين
    public function availableCollectors(): JsonResponse
    {
        try {
            $collectors = User::where('role', 'collector')
                             ->where('status', 'active')
                             ->select('id', 'name', 'username')
                             ->get();
            return JsonResponseHelper::success($collectors, 'تم جلب المحصلين بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في جلب المحصلين: ' . $e->getMessage(), 500);
        }
    }

    // الحصول على المراجعين المتاحين
    public function availableReviewers(): JsonResponse
    {
        try {
            $reviewers = User::where('role', 'reviewer')
                            ->where('status', 'active')
                            ->select('id', 'name', 'username')
                            ->get();
            return JsonResponseHelper::success($reviewers, 'تم جلب المراجعين بنجاح');
        } catch (\Exception $e) {
            return JsonResponseHelper::error('خطأ في جلب المراجعين: ' . $e->getMessage(), 500);
        }
    }
}
