<?php

namespace App\Http\Requests;

use App\Models\Subscriber;
use App\Enums\UserRole;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreReadingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only collectors can create readings
        return $this->user()?->getRoleEnum() === UserRole::COLLECTOR;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'subscriber_id' => [
                'required',
                'integer',
                Rule::exists('subscribers', 'id')->where(function ($query) {
                    // Ensure collector can only add readings for subscribers in their zones
                    if ($this->user()?->isCollector()) {
                        $collectorZones = $this->user()->zones->pluck('id');
                        $query->whereIn('zone_id', $collectorZones);
                    }
                }),
            ],
            'previous_reading' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'current_reading' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99',
                'gte:previous_reading', // Current reading must be >= previous reading
            ],
            'image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,webp',
                'max:2048', // 2MB max
                'dimensions:min_width=100,min_height=100,max_width=4000,max_height=4000',
            ],
            'bill_status' => [
                'nullable',
                'string',
                'in:paid,not_paid,late',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'subscriber_id.required' => 'يجب اختيار المشترك',
            'subscriber_id.exists' => 'المشترك المحدد غير موجود أو غير مسموح لك بإضافة قراءة له',
            'previous_reading.required' => 'يجب إدخال القراءة السابقة',
            'previous_reading.numeric' => 'القراءة السابقة يجب أن تكون رقماً',
            'previous_reading.min' => 'القراءة السابقة يجب أن تكون أكبر من أو تساوي صفر',
            'current_reading.required' => 'يجب إدخال القراءة الحالية',
            'current_reading.numeric' => 'القراءة الحالية يجب أن تكون رقماً',
            'current_reading.min' => 'القراءة الحالية يجب أن تكون أكبر من أو تساوي صفر',
            'current_reading.gte' => 'القراءة الحالية يجب أن تكون أكبر من أو تساوي القراءة السابقة',
            'image.image' => 'الملف المرفوع يجب أن يكون صورة',
            'image.mimes' => 'الصورة يجب أن تكون من نوع: jpeg, png, jpg, webp',
            'image.max' => 'حجم الصورة يجب أن يكون أقل من 2 ميجابايت',
            'image.dimensions' => 'أبعاد الصورة غير مناسبة (الحد الأدنى: 100x100، الحد الأقصى: 4000x4000)',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'subscriber_id' => 'المشترك',
            'previous_reading' => 'القراءة السابقة',
            'current_reading' => 'القراءة الحالية',
            'image' => 'صورة العداد',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Ensure numeric values are properly formatted
        $this->merge([
            'previous_reading' => is_numeric($this->previous_reading)
                ? (float) $this->previous_reading
                : $this->previous_reading,
            'current_reading' => is_numeric($this->current_reading)
                ? (float) $this->current_reading
                : $this->current_reading,
        ]);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator): void
    {
        // Log validation failures for debugging
        logger()->warning('Reading validation failed', [
            'user_id' => $this->user()?->id,
            'errors' => $validator->errors()->toArray(),
            'input' => $this->except(['image']), // Don't log image data
        ]);

        parent::failedValidation($validator);
    }
}
