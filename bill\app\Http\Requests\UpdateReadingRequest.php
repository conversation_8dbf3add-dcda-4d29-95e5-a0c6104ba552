<?php

namespace App\Http\Requests;

use App\Models\Reading;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateReadingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $reading = $this->route('reading');
        
        // Only allow updates if reading is pending
        if ($reading->status !== 'pending') {
            return false;
        }
        
        // Only the collector who created the reading or admin can update
        return $this->user()?->isAdmin() || 
               ($this->user()?->isCollector() && $reading->collector_id === $this->user()->id);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'previous_reading' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99',
            ],
            'current_reading' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99',
                'gte:previous_reading',
            ],
            'image' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,webp',
                'max:2048',
                'dimensions:min_width=100,min_height=100,max_width=4000,max_height=4000',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'previous_reading.required' => 'يجب إدخال القراءة السابقة',
            'previous_reading.numeric' => 'القراءة السابقة يجب أن تكون رقماً',
            'previous_reading.min' => 'القراءة السابقة يجب أن تكون أكبر من أو تساوي صفر',
            'current_reading.required' => 'يجب إدخال القراءة الحالية',
            'current_reading.numeric' => 'القراءة الحالية يجب أن تكون رقماً',
            'current_reading.min' => 'القراءة الحالية يجب أن تكون أكبر من أو تساوي صفر',
            'current_reading.gte' => 'القراءة الحالية يجب أن تكون أكبر من أو تساوي القراءة السابقة',
            'image.image' => 'الملف المرفوع يجب أن يكون صورة',
            'image.mimes' => 'الصورة يجب أن تكون من نوع: jpeg, png, jpg, webp',
            'image.max' => 'حجم الصورة يجب أن يكون أقل من 2 ميجابايت',
            'image.dimensions' => 'أبعاد الصورة غير مناسبة',
        ];
    }
}
