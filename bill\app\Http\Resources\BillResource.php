<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BillResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'reading_id' => $this->reading_id,
            'amount' => $this->amount,
            'amount_formatted' => number_format($this->amount, 2) . ' ريال',
            'issued_at' => $this->issued_at?->format('Y-m-d H:i:s'),
            'issued_at_formatted' => $this->issued_at?->format('d/m/Y'),
            'is_paid' => $this->is_paid,
            'paid_at' => $this->paid_at?->format('Y-m-d H:i:s'),
            'paid_at_formatted' => $this->paid_at?->format('d/m/Y'),
            'status' => $this->is_paid ? 'مدفوعة' : 'غير مدفوعة',
            'is_overdue' => $this->isOverdue(),
            'days_overdue' => $this->when(
                $this->isOverdue(),
                fn() => $this->issued_at->diffInDays(now())
            ),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Related data
            'reading' => new ReadingResource($this->whenLoaded('reading')),
            'subscriber' => new SubscriberResource($this->whenLoaded('subscriber')),
            
            // Permissions
            'can_mark_paid' => $this->when(
                $request->user(),
                fn() => $request->user()->can('markAsPaid', $this->resource)
            ),
            'can_edit' => $this->when(
                $request->user(),
                fn() => $request->user()->can('update', $this->resource)
            ),
            'can_delete' => $this->when(
                $request->user(),
                fn() => $request->user()->can('delete', $this->resource)
            ),
        ];
    }
}
