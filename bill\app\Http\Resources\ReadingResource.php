<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReadingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'subscriber_id' => $this->subscriber_id,
            'collector_id' => $this->collector_id,
            'previous_reading' => $this->previous_reading,
            'current_reading' => $this->current_reading,
            'consumption' => $this->current_reading - $this->previous_reading,
            'image_path' => $this->image_path,
            'image_url' => $this->when($this->image_path, 
                fn() => asset('storage/' . $this->image_path)
            ),
            'status' => $this->status,
            'status_label' => match($this->status) {
                'pending' => 'معلقة',
                'approved' => 'معتمدة',
                'rejected' => 'مرفوضة',
                default => $this->status,
            },
            'reviewed_by' => $this->reviewed_by,
            'review_note' => $this->review_note,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Related data
            'subscriber' => new SubscriberResource($this->whenLoaded('subscriber')),
            'collector' => new UserResource($this->whenLoaded('collector')),
            'reviewer' => new UserResource($this->whenLoaded('reviewer')),
            'bill' => new BillResource($this->whenLoaded('bill')),
            
            // Permissions for current user
            'can_edit' => $this->when(
                $request->user(),
                fn() => $request->user()->can('update', $this->resource)
            ),
            'can_delete' => $this->when(
                $request->user(),
                fn() => $request->user()->can('delete', $this->resource)
            ),
            'can_approve' => $this->when(
                $request->user(),
                fn() => $request->user()->can('approve', $this->resource)
            ),
            'can_reject' => $this->when(
                $request->user(),
                fn() => $request->user()->can('reject', $this->resource)
            ),
        ];
    }
}
