<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriberResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'subscription_no' => $this->subscription_no,
            'address' => $this->address,
            'zone_id' => $this->zone_id,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Related data
            'zone' => new ZoneResource($this->whenLoaded('zone')),
            
            // Statistics
            'latest_reading' => new ReadingResource($this->whenLoaded('latestReading')),
            'total_readings' => $this->when(
                $request->user()?->hasRole('admin', 'reviewer'),
                fn() => $this->readings()->count()
            ),
            'pending_readings' => $this->when(
                $request->user()?->hasRole('admin', 'reviewer'),
                fn() => $this->readings()->where('status', 'pending')->count()
            ),
            'unpaid_bills' => $this->when(
                $request->user()?->hasRole('admin', 'reviewer', 'collector'),
                fn() => $this->bills()->where('is_paid', false)->count()
            ),
            'total_debt' => $this->when(
                $request->user()?->hasRole('admin', 'reviewer', 'collector'),
                fn() => $this->bills()->where('is_paid', false)->sum('amount')
            ),
            
            // Permissions
            'can_edit' => $this->when(
                $request->user(),
                fn() => $request->user()->can('update', $this->resource)
            ),
            'can_delete' => $this->when(
                $request->user(),
                fn() => $request->user()->can('delete', $this->resource)
            ),
        ];
    }
}
