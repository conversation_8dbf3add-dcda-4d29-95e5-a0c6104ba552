<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'username' => $this->username,
            'name' => $this->name,
            'phone' => $this->phone,
            'role' => $this->role,
            'role_name_ar' => $this->getRoleNameAr(),
            'area' => $this->area,
            'status' => $this->status,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Conditional fields based on user permissions
            'permissions' => $this->when($request->user()?->isAdmin(), [
                'can_manage_zones' => $this->getRoleEnum()?->canManageZones() ?? false,
                'can_manage_subscribers' => $this->getRoleEnum()?->canManageSubscribers() ?? false,
                'can_create_readings' => $this->getRoleEnum()?->canCreateReadings() ?? false,
                'can_review_readings' => $this->getRoleEnum()?->canReviewReadings() ?? false,
                'can_create_bills' => $this->getRoleEnum()?->canCreateBills() ?? false,
                'can_view_bills' => $this->getRoleEnum()?->canViewBills() ?? false,
                'can_mark_bills_paid' => $this->getRoleEnum()?->canMarkBillsAsPaid() ?? false,
            ]),
        ];
    }
}
