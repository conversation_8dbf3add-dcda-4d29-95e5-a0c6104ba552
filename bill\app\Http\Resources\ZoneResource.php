<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ZoneResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'collector_id' => $this->collector_id,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Related data
            'collector' => new UserResource($this->whenLoaded('collector')),
            'subscribers' => SubscriberResource::collection($this->whenLoaded('subscribers')),
            
            // Statistics
            'subscribers_count' => $this->when(
                $request->user()?->hasRole('admin', 'reviewer'),
                fn() => $this->subscribers()->count()
            ),
            'pending_readings_count' => $this->when(
                $request->user()?->hasRole('admin', 'reviewer'),
                fn() => $this->subscribers()
                    ->join('readings', 'subscribers.id', '=', 'readings.subscriber_id')
                    ->where('readings.status', 'pending')
                    ->count()
            ),
            'unpaid_bills_count' => $this->when(
                $request->user()?->hasRole('admin', 'reviewer'),
                fn() => $this->subscribers()
                    ->join('readings', 'subscribers.id', '=', 'readings.subscriber_id')
                    ->join('bills', 'readings.id', '=', 'bills.reading_id')
                    ->where('bills.is_paid', false)
                    ->count()
            ),
            
            // Permissions
            'can_edit' => $this->when(
                $request->user(),
                fn() => $request->user()->can('update', $this->resource)
            ),
            'can_delete' => $this->when(
                $request->user(),
                fn() => $request->user()->can('delete', $this->resource)
            ),
        ];
    }
}
