<?php

namespace App\Listeners;

use App\Events\ReadingCreated;
use App\Events\ReadingStatusChanged;
use App\Services\ReadingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Cache;

class ClearReadingCache implements ShouldQueue
{
    use InteractsWithQueue;

    protected ReadingService $readingService;

    /**
     * Create the event listener.
     */
    public function __construct(ReadingService $readingService)
    {
        $this->readingService = $readingService;
    }

    /**
     * Handle the event.
     */
    public function handle(ReadingCreated|ReadingStatusChanged $event): void
    {
        // Clear cache for the collector
        $this->readingService->clearUserCache($event->reading->collector);
        
        // Clear general cache keys
        Cache::forget('zone_performance');
        
        // Clear admin dashboard cache
        Cache::forget('dashboard_stats_admin');
        
        // Clear reviewer cache if status changed
        if ($event instanceof ReadingStatusChanged && isset($event->reviewer)) {
            $this->readingService->clearUserCache($event->reviewer);
        }
    }
}
