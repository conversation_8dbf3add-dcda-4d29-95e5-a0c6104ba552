<?php

namespace App\Listeners;

use App\Events\ReadingCreated;
use App\Events\ReadingStatusChanged;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class LogReadingActivity implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle reading created event.
     */
    public function handle(ReadingCreated|ReadingStatusChanged $event): void
    {
        if ($event instanceof ReadingCreated) {
            Log::info('New reading created', [
                'reading_id' => $event->reading->id,
                'subscriber_id' => $event->reading->subscriber_id,
                'collector_id' => $event->reading->collector_id,
                'current_reading' => $event->reading->current_reading,
                'consumption' => $event->reading->consumption,
                'created_at' => $event->reading->created_at,
            ]);
        }

        if ($event instanceof ReadingStatusChanged) {
            Log::info('Reading status changed', [
                'reading_id' => $event->reading->id,
                'subscriber_id' => $event->reading->subscriber_id,
                'collector_id' => $event->reading->collector_id,
                'old_status' => $event->oldStatus,
                'new_status' => $event->newStatus,
                'reviewer_id' => $event->reviewer->id,
                'review_note' => $event->reading->review_note,
                'changed_at' => now(),
            ]);
        }
    }
}
