<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bill extends Model
{
    use HasFactory;

    protected $fillable = [
        'reading_id',
        'amount',
        'issued_at',
        'is_paid',
        'paid_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'issued_at' => 'datetime',
        'paid_at' => 'datetime',
        'is_paid' => 'boolean',
    ];

    // العلاقة مع القراءة
    public function reading()
    {
        return $this->belongsTo(Reading::class);
    }

    // العلاقة مع المشترك عبر القراءة
    public function subscriber()
    {
        return $this->hasOneThrough(
            Subscriber::class,
            Reading::class,
            'id',          // مفتاح أجنبي في جدول القراءة (Reading)
            'id',          // مفتاح أجنبي في جدول المشترك (Subscriber)
            'reading_id',  // المفتاح المحلي في جدول الفواتير (Bill)
            'subscriber_id'// المفتاح المحلي في جدول القراءة (Reading)
        );
    }

    // تحقق من حالة الدفع
    public function isPaid()
    {
        return $this->is_paid;
    }

    public function isOverdue()
    {
        return !$this->is_paid && $this->issued_at->addDays(30)->isPast();
    }

    // تحديد الفاتورة كمدفوعة
    public function markAsPaid()
    {
        $this->update([
            'is_paid' => true,
            'paid_at' => now(),
        ]);
    }
}

