<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Reading extends Model
{
    use HasFactory;

    protected $fillable = [
        'subscriber_id',
        'collector_id',
        'previous_reading',
        'current_reading',
        'image_path',
        'status',
        'bill_status',
        'bill_amount',
        'reviewed_by',
        'review_note',
    ];

    protected $casts = [
        'previous_reading' => 'decimal:2',
        'current_reading' => 'decimal:2',
        'bill_amount' => 'decimal:2',
        'status' => 'string',
        'bill_status' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * العلاقة مع المشترك
     */
    public function subscriber(): BelongsTo
    {
        return $this->belongsTo(Subscriber::class);
    }

    /**
     * العلاقة مع المحصل
     */
    public function collector(): BelongsTo
    {
        return $this->belongsTo(User::class, 'collector_id');
    }

    /**
     * العلاقة مع المراجع
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * العلاقة مع الفاتورة
     */
    public function bill(): HasOne
    {
        return $this->hasOne(Bill::class);
    }

    /**
     * حساب الاستهلاك (فرق القراءتين) - Laravel 12 Attribute
     */
    protected function consumption(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->current_reading - $this->previous_reading,
        );
    }

    /**
     * تحقق من أن القراءة معلقة
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * تحقق من أن القراءة تمت الموافقة عليها
     */
    public function isApproved()
    {
        return $this->status === 'approved';
    }

    /**
     * تحقق من أن القراءة تم رفضها
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Scope للقراءات المعلقة
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope للقراءات المعتمدة
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope للقراءات المرفوضة
     */
    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope للقراءات حسب المحصل
     */
    public function scopeByCollector(Builder $query, int $collectorId): Builder
    {
        return $query->where('collector_id', $collectorId);
    }

    /**
     * Scope للقراءات حسب المنطقة
     */
    public function scopeByZone(Builder $query, int $zoneId): Builder
    {
        return $query->whereHas('subscriber', function ($q) use ($zoneId) {
            $q->where('zone_id', $zoneId);
        });
    }
}
