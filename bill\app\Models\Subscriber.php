<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscriber extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'subscription_no',
        'address',
        'phone',
        'email',
        'zone_id',
        'meter_number',
        'connection_date',
        'notes',
    ];

    protected $casts = [
        'connection_date' => 'date',
    ];

    // العلاقة مع المنطقة
    public function zone()
    {
        return $this->belongsTo(Zone::class);
    }

    // العلاقة مع القراءات
    public function readings()
    {
        return $this->hasMany(Reading::class);
    }

    // العلاقة مع الفواتير عبر القراءات
    public function bills()
    {
        return $this->hasManyThrough(Bill::class, Reading::class);
    }

    // الحصول على آخر قراءة
    public function latestReading()
    {
        return $this->hasOne(Reading::class)->latest();
    }
}
