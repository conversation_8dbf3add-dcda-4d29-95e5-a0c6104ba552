<?php

namespace App\Models;

use App\Enums\UserRole;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    // Role constants (for backward compatibility)
    public const ROLE_ADMIN = 'admin';
    public const ROLE_COLLECTOR = 'collector';
    public const ROLE_REVIEWER = 'reviewer';

    protected $fillable = [
        'user_id',
        'username',
        'name',
        'phone',
        'password',
        'role',
        'area',
        'status',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'password' => 'hashed',
    ];

    // العلاقة مع القراءات (قراءات قام بها المحصل)
    public function readings()
    {
        return $this->hasMany(Reading::class, 'collector_id');
    }

    // العلاقة مع القراءات المراجعة (قراءات راجعها المراجع)
    public function reviewedReadings()
    {
        return $this->hasMany(Reading::class, 'reviewed_by');
    }

    // العلاقة مع المناطق (للمحصلين)
    public function zones()
    {
        return $this->hasMany(Zone::class, 'collector_id');
    }

    // تحقق من الدور
    public function isAdmin()
    {
        return $this->role === self::ROLE_ADMIN;
    }

    public function isCollector()
    {
        return $this->role === self::ROLE_COLLECTOR;
    }

    public function isReviewer()
    {
        return $this->role === self::ROLE_REVIEWER;
    }

    // Helper method to get Arabic role name
    public function getRoleNameAr()
    {
        return UserRole::getArabicNames()[$this->role] ?? $this->role;
    }

    // Helper method to get role enum
    public function getRoleEnum(): ?UserRole
    {
        return UserRole::tryFrom($this->role);
    }

    // Helper method to check if user has any of the given roles
    public function hasRole(...$roles)
    {
        return in_array($this->role, $roles);
    }
}
