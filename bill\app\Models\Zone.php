<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Zone extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'collector_id',
    ];

    // العلاقة مع المحصل
    public function collector()
    {
        return $this->belongsTo(User::class, 'collector_id');
    }

    // العلاقة مع المشتركين
    public function subscribers()
    {
        return $this->hasMany(Subscriber::class);
    }
}