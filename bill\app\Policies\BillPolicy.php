<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Bill;
use App\Enums\UserRole;

class BillPolicy
{
    public function viewAny(User $user)
    {
        // استخدام الـ enum للتحقق من صلاحية رؤية الفواتير
        $roleEnum = $user->getRoleEnum();
        return $roleEnum?->canViewBills() ?? false;
    }

    public function view(User $user, Bill $bill)
    {
        // المدير والمراجع يمكنهم رؤية كل الفواتير
        if ($user->hasRole(User::ROLE_ADMIN, User::ROLE_REVIEWER)) {
            return true;
        }

        // المحصل يمكنه رؤية الفواتير المرتبطة بمنطقته فقط
        if ($user->isCollector()) {
            return $bill->reading->subscriber->zone->collector_id === $user->id;
        }

        return false;
    }

    public function create(User $user)
    {
        // استخدام الـ enum للتحقق من صلاحية إنشاء الفواتير
        $roleEnum = $user->getRoleEnum();
        return $roleEnum?->canCreateBills() ?? false;
    }

    public function update(User $user, Bill $bill)
    {
        // المدير فقط يمكنه تعديل الفاتورة
        return $user->isAdmin();
    }

    public function markAsPaid(User $user, Bill $bill)
    {
        // استخدام الـ enum للتحقق من صلاحية تحديد الفاتورة كمدفوعة
        $roleEnum = $user->getRoleEnum();
        return $roleEnum?->canMarkBillsAsPaid() ?? false;
    }

    public function delete(User $user, Bill $bill)
    {
        // المدير فقط يمكنه حذف الفاتورة
        return $user->isAdmin();
    }
}
