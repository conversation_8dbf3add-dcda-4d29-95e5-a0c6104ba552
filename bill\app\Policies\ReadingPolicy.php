<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Reading;

class ReadingPolicy
{
    /**
     * المحصل فقط يملك صلاحية إنشاء قراءة جديدة.
     */
    public function create(User $user)
    {
        return $user->isCollector();
    }

    /**
     * المراجع فقط يستطيع الموافقة على القراءة.
     */
    public function approve(User $user, Reading $reading)
    {
        return $user->isReviewer();
    }

    /**
     * المراجع فقط يستطيع رفض القراءة.
     */
    public function reject(User $user, Reading $reading)
    {
        return $user->isReviewer();
    }

    /**
     * المدير والمراجع يمكنهم مشاهدة التفاصيل.
     */
    public function view(User $user, Reading $reading)
    {
        return $user->hasRole(User::ROLE_ADMIN, User::ROLE_REVIEWER);
    }

    /**
     * المدير فقط يمكنه حذف القراءة.
     */
    public function delete(User $user, Reading $reading)
    {
        return $user->isAdmin();
    }

    /**
     * المحصل يمكنه تعديل القراءة فقط إذا كانت معلقة.
     * المدير يمكنه تعديل أي قراءة.
     */
    public function update(User $user, Reading $reading)
    {
        if ($user->role === 'محصل' && $reading->status === 'pending') {
            return true;
        }

        return $user->role === 'مدير';
    }
}
