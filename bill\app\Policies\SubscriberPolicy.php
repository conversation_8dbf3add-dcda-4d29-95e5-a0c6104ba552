<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Subscriber;

class SubscriberPolicy
{
    public function viewAny(User $user)
    {
        return $user->hasRole(User::ROLE_ADMIN, User::ROLE_REVIEWER);
    }

    public function view(User $user, Subscriber $subscriber)
    {
        return $user->hasRole(User::ROLE_ADMIN, User::ROLE_REVIEWER);
    }

    public function create(User $user)
    {
        return $user->isAdmin();
    }

    public function update(User $user, Subscriber $subscriber)
    {
        return $user->isAdmin();
    }

    public function delete(User $user, Subscriber $subscriber)
    {
        return $user->isAdmin();
    }
}
