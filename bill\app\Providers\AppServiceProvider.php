<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Http\JsonResponse;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set default JSON response options to handle Arabic text properly
        JsonResponse::macro('withUnicodeSupport', function ($data = null, $status = 200, $headers = [], $options = 0) {
            return new JsonResponse($data, $status, $headers, JSON_UNESCAPED_UNICODE | $options);
        });
    }
}
