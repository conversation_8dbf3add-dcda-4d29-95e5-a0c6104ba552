<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

use App\Models\Reading;
use App\Policies\ReadingPolicy;
use App\Models\Subscriber;
use App\Policies\SubscriberPolicy;
use App\Models\Zone;
use App\Policies\ZonePolicy;
use App\Models\Bill;
use App\Policies\BillPolicy;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     */
    protected $policies = [
        Reading::class => ReadingPolicy::class,
        Subscriber::class => SubscriberPolicy::class,
        Zone::class => ZonePolicy::class,
        Bill::class => BillPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // يمكن هنا تسجيل أبواب إضافية إذا احتجت
        // Gate::define('some-ability', function (User $user) {
        //     return $user->role === 'admin';
        // });
    }
}
