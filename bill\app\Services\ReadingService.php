<?php

namespace App\Services;

use App\Models\Reading;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ReadingService
{
    /**
     * Get dashboard statistics with caching
     */
    public function getDashboardStats(User $user): array
    {
        $cacheKey = "dashboard_stats_{$user->id}_{$user->role}";
        
        return Cache::remember($cacheKey, now()->addMinutes(10), function () use ($user) {
            $stats = [];
            
            if ($user->isAdmin()) {
                $stats = [
                    'total_readings' => Reading::count(),
                    'pending_readings' => Reading::pending()->count(),
                    'approved_readings' => Reading::approved()->count(),
                    'rejected_readings' => Reading::rejected()->count(),
                    'readings_today' => Reading::whereDate('created_at', today())->count(),
                    'readings_this_month' => Reading::whereMonth('created_at', now()->month)->count(),
                ];
            } elseif ($user->isCollector()) {
                $stats = [
                    'my_readings' => Reading::byCollector($user->id)->count(),
                    'my_pending_readings' => Reading::byCollector($user->id)->pending()->count(),
                    'my_approved_readings' => Reading::byCollector($user->id)->approved()->count(),
                    'my_rejected_readings' => Reading::byCollector($user->id)->rejected()->count(),
                    'readings_today' => Reading::byCollector($user->id)->whereDate('created_at', today())->count(),
                ];
            } elseif ($user->isReviewer()) {
                $stats = [
                    'pending_for_review' => Reading::pending()->count(),
                    'reviewed_by_me' => Reading::where('reviewed_by', $user->id)->count(),
                    'approved_by_me' => Reading::where('reviewed_by', $user->id)->approved()->count(),
                    'rejected_by_me' => Reading::where('reviewed_by', $user->id)->rejected()->count(),
                ];
            }
            
            return $stats;
        });
    }

    /**
     * Get reading trends for charts
     */
    public function getReadingTrends(User $user, int $days = 30): array
    {
        $cacheKey = "reading_trends_{$user->id}_{$user->role}_{$days}";
        
        return Cache::remember($cacheKey, now()->addHours(1), function () use ($user, $days) {
            $query = Reading::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->where('created_at', '>=', now()->subDays($days))
                ->groupBy('date')
                ->orderBy('date');
            
            if ($user->isCollector()) {
                $query->byCollector($user->id);
            }
            
            return $query->get()->toArray();
        });
    }

    /**
     * Get consumption analysis
     */
    public function getConsumptionAnalysis(User $user): array
    {
        $cacheKey = "consumption_analysis_{$user->id}_{$user->role}";
        
        return Cache::remember($cacheKey, now()->addHours(2), function () use ($user) {
            $query = Reading::selectRaw('
                AVG(current_reading - previous_reading) as avg_consumption,
                MIN(current_reading - previous_reading) as min_consumption,
                MAX(current_reading - previous_reading) as max_consumption,
                COUNT(*) as total_readings
            ')->approved();
            
            if ($user->isCollector()) {
                $query->byCollector($user->id);
            }
            
            $result = $query->first();
            
            return [
                'average_consumption' => round($result->avg_consumption ?? 0, 2),
                'minimum_consumption' => $result->min_consumption ?? 0,
                'maximum_consumption' => $result->max_consumption ?? 0,
                'total_readings' => $result->total_readings ?? 0,
            ];
        });
    }

    /**
     * Clear user-specific cache
     */
    public function clearUserCache(User $user): void
    {
        $patterns = [
            "dashboard_stats_{$user->id}_{$user->role}",
            "reading_trends_{$user->id}_{$user->role}_*",
            "consumption_analysis_{$user->id}_{$user->role}",
        ];
        
        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // For wildcard patterns, we'd need to implement cache tag clearing
                // For now, we'll clear specific keys
                for ($i = 7; $i <= 90; $i += 7) {
                    Cache::forget(str_replace('*', $i, $pattern));
                }
            } else {
                Cache::forget($pattern);
            }
        }
    }

    /**
     * Get zone performance metrics
     */
    public function getZonePerformance(): array
    {
        return Cache::remember('zone_performance', now()->addHours(6), function () {
            return DB::table('zones')
                ->leftJoin('subscribers', 'zones.id', '=', 'subscribers.zone_id')
                ->leftJoin('readings', 'subscribers.id', '=', 'readings.subscriber_id')
                ->select([
                    'zones.id',
                    'zones.name',
                    DB::raw('COUNT(DISTINCT subscribers.id) as subscribers_count'),
                    DB::raw('COUNT(readings.id) as total_readings'),
                    DB::raw('COUNT(CASE WHEN readings.status = "pending" THEN 1 END) as pending_readings'),
                    DB::raw('COUNT(CASE WHEN readings.status = "approved" THEN 1 END) as approved_readings'),
                    DB::raw('COUNT(CASE WHEN readings.status = "rejected" THEN 1 END) as rejected_readings'),
                ])
                ->groupBy('zones.id', 'zones.name')
                ->get()
                ->toArray();
        });
    }
}
