<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة حقول جديدة لجدول zones
        Schema::table('zones', function (Blueprint $table) {
            $table->unsignedBigInteger('reviewer_id')->nullable()->after('collector_id');
            $table->text('description')->nullable()->after('name');
            
            $table->foreign('reviewer_id')->references('id')->on('users')->onDelete('set null');
        });

        // إضافة حقول جديدة لجدول subscribers
        Schema::table('subscribers', function (Blueprint $table) {
            $table->string('phone', 20)->nullable()->after('address');
            $table->string('email')->nullable()->after('phone');
            $table->string('meter_number', 100)->nullable()->after('zone_id');
            $table->date('connection_date')->nullable()->after('meter_number');
            $table->text('notes')->nullable()->after('connection_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('zones', function (Blueprint $table) {
            $table->dropForeign(['reviewer_id']);
            $table->dropColumn(['reviewer_id', 'description']);
        });

        Schema::table('subscribers', function (Blueprint $table) {
            $table->dropColumn(['phone', 'email', 'meter_number', 'connection_date', 'notes']);
        });
    }
};
