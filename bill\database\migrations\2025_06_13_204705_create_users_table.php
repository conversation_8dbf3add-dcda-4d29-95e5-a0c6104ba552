<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('user_id')->unique()->nullable(); // معرف النظام (nullable لدعم التوافق)
            $table->string('username')->unique();            // لتسجيل الدخول
            $table->string('name')->nullable();              // اسم المستخدم الكامل (nullable لدعم التوافق)
            $table->string('phone')->unique()->nullable();   // رقم الهاتف (nullable لدعم التوافق)
            $table->string('password');                      // كلمة المرور المشفرة
            $table->enum('role', ['admin', 'collector', 'reviewer']); // الدور
            $table->string('area')->nullable();              // المنطقة للمحصلين
            $table->string('status')->default('active');     // الحالة
            $table->timestamps();                            // created_at + updated_at
        });
    }

    public function down(): void {
        Schema::dropIfExists('users');
    }
};
