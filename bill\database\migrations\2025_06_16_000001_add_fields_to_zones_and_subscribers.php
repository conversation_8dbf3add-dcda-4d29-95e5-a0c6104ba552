<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة حقول جديدة لجدول zones
        if (!Schema::hasColumn('zones', 'reviewer_id')) {
            Schema::table('zones', function (Blueprint $table) {
                $table->unsignedBigInteger('reviewer_id')->nullable()->after('collector_id');
                $table->foreign('reviewer_id')->references('id')->on('users')->onDelete('set null');
            });
        }
        
        if (!Schema::hasColumn('zones', 'description')) {
            Schema::table('zones', function (Blueprint $table) {
                $table->text('description')->nullable()->after('name');
            });
        }

        // إضافة حقول جديدة لجدول subscribers
        if (!Schema::hasColumn('subscribers', 'phone')) {
            Schema::table('subscribers', function (Blueprint $table) {
                $table->string('phone', 20)->nullable()->after('address');
            });
        }
        
        if (!Schema::hasColumn('subscribers', 'email')) {
            Schema::table('subscribers', function (Blueprint $table) {
                $table->string('email')->nullable()->after('phone');
            });
        }
        
        if (!Schema::hasColumn('subscribers', 'meter_number')) {
            Schema::table('subscribers', function (Blueprint $table) {
                $table->string('meter_number', 100)->nullable()->after('zone_id');
            });
        }
        
        if (!Schema::hasColumn('subscribers', 'connection_date')) {
            Schema::table('subscribers', function (Blueprint $table) {
                $table->date('connection_date')->nullable()->after('meter_number');
            });
        }
        
        if (!Schema::hasColumn('subscribers', 'notes')) {
            Schema::table('subscribers', function (Blueprint $table) {
                $table->text('notes')->nullable()->after('connection_date');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('zones', function (Blueprint $table) {
            if (Schema::hasColumn('zones', 'reviewer_id')) {
                $table->dropForeign(['reviewer_id']);
                $table->dropColumn('reviewer_id');
            }
            if (Schema::hasColumn('zones', 'description')) {
                $table->dropColumn('description');
            }
        });

        Schema::table('subscribers', function (Blueprint $table) {
            $columnsToCheck = ['phone', 'email', 'meter_number', 'connection_date', 'notes'];
            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('subscribers', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
