<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('readings', function (Blueprint $table) {
            $table->enum('bill_status', ['paid', 'not_paid', 'late'])->default('not_paid')->after('status');
            $table->decimal('bill_amount', 10, 2)->nullable()->after('bill_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('readings', function (Blueprint $table) {
            $table->dropColumn(['bill_status', 'bill_amount']);
        });
    }
};
