<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Zone;
use App\Models\Subscriber;
use App\Models\Reading;
use App\Models\Bill;
use Illuminate\Support\Facades\Hash;

class ArabicSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم تجريبي
        $user = User::create([
            'name' => 'مدير النظام',
            'username' => 'admin',
            'password' => Hash::make('password123'),
            'role' => 'مدير',
        ]);

        // إنشاء مناطق تجريبية
        $zone1 = Zone::create([
            'name' => 'المنطقة الشمالية',
            'description' => 'تشمل الأحياء الشمالية من المدينة',
            'rate_per_unit' => 0.25,
        ]);

        $zone2 = Zone::create([
            'name' => 'المنطقة الجنوبية',
            'description' => 'تشمل الأحياء الجنوبية من المدينة',
            'rate_per_unit' => 0.30,
        ]);

        $zone3 = Zone::create([
            'name' => 'المنطقة الوسطى',
            'description' => 'تشمل وسط المدينة والمناطق التجارية',
            'rate_per_unit' => 0.35,
        ]);

        // إنشاء مشتركين تجريبيين
        $subscriber1 = Subscriber::create([
            'name' => 'أحمد محمد علي',
            'email' => '<EMAIL>',
            'phone' => '0501234567',
            'address' => 'شارع الملك فهد، حي النخيل',
            'zone_id' => $zone1->id,
            'meter_number' => 'MTR001',
        ]);

        $subscriber2 = Subscriber::create([
            'name' => 'فاطمة عبدالله',
            'email' => '<EMAIL>',
            'phone' => '0507654321',
            'address' => 'شارع الأمير سلطان، حي الورود',
            'zone_id' => $zone2->id,
            'meter_number' => 'MTR002',
        ]);

        $subscriber3 = Subscriber::create([
            'name' => 'محمد سعد الدين',
            'email' => '<EMAIL>',
            'phone' => '0509876543',
            'address' => 'شارع العليا، حي السفارات',
            'zone_id' => $zone3->id,
            'meter_number' => 'MTR003',
        ]);

        // إ��شاء قراءات تجريبية
        $reading1 = Reading::create([
            'user_id' => $user->id,
            'subscriber_id' => $subscriber1->id,
            'previous_reading' => 1000,
            'current_reading' => 1250,
            'reading_date' => now()->subDays(5),
            'units_consumed' => 250,
            'status' => Reading::STATUS_APPROVED,
            'approved_by' => $user->id,
            'approved_at' => now()->subDays(4),
        ]);

        $reading2 = Reading::create([
            'user_id' => $user->id,
            'subscriber_id' => $subscriber2->id,
            'previous_reading' => 800,
            'current_reading' => 1100,
            'reading_date' => now()->subDays(3),
            'units_consumed' => 300,
            'status' => Reading::STATUS_PENDING,
        ]);

        $reading3 = Reading::create([
            'user_id' => $user->id,
            'subscriber_id' => $subscriber3->id,
            'previous_reading' => 1500,
            'current_reading' => 1650,
            'reading_date' => now()->subDays(2),
            'units_consumed' => 150,
            'status' => Reading::STATUS_APPROVED,
            'approved_by' => $user->id,
            'approved_at' => now()->subDays(1),
        ]);

        // إنشاء فواتير تجريبية
        Bill::create([
            'subscriber_id' => $subscriber1->id,
            'reading_id' => $reading1->id,
            'bill_number' => 'BILL-2025-000001',
            'amount' => $reading1->units_consumed * $zone1->rate_per_unit,
            'units_consumed' => $reading1->units_consumed,
            'rate_per_unit' => $zone1->rate_per_unit,
            'due_date' => now()->addDays(30),
            'status' => Bill::STATUS_PENDING,
            'generated_by' => $user->id,
        ]);

        Bill::create([
            'subscriber_id' => $subscriber3->id,
            'reading_id' => $reading3->id,
            'bill_number' => 'BILL-2025-000002',
            'amount' => $reading3->units_consumed * $zone3->rate_per_unit,
            'units_consumed' => $reading3->units_consumed,
            'rate_per_unit' => $zone3->rate_per_unit,
            'due_date' => now()->addDays(25),
            'status' => Bill::STATUS_PAID,
            'generated_by' => $user->id,
            'paid_at' => now(),
            'payment_method' => 'نقداً',
        ]);
    }
}