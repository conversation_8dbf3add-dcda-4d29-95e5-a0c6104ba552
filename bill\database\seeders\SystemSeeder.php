<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Zone;
use App\Models\Subscriber;
use App\Models\Reading;
use App\Models\Bill;

class SystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء المستخدمين
        $admin = User::create([
            'user_id' => 'U-ADMIN',
            'username' => 'admin',
            'name' => 'Admin User',
            'phone' => '0500000001',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'area' => null,
        ]);

        $collector1 = User::create([
            'user_id' => 'U-COL1',
            'username' => 'collector1',
            'name' => 'Collector 1',
            'phone' => '0500000002',
            'password' => Hash::make('password'),
            'role' => 'collector',
            'area' => 'المنطقة الشمالية',
        ]);

        $collector2 = User::create([
            'user_id' => 'U-COL2',
            'username' => 'collector2',
            'name' => 'Collector 2',
            'phone' => '0500000003',
            'password' => Hash::make('password'),
            'role' => 'collector',
            'area' => 'المنطقة الجنوبية',
        ]);

        $reviewer1 = User::create([
            'user_id' => 'U-REV1',
            'username' => 'reviewer1',
            'name' => 'Reviewer 1',
            'phone' => '0500000004',
            'password' => Hash::make('password'),
            'role' => 'reviewer',
            'area' => null,
        ]);

        $reviewer2 = User::create([
            'user_id' => 'U-REV2',
            'username' => 'reviewer2',
            'name' => 'Reviewer 2',
            'phone' => '0500000005',
            'password' => Hash::make('password'),
            'role' => 'reviewer',
            'area' => null,
        ]);

        // إنشاء المناطق
        $zone1 = Zone::create([
            'name' => 'المنطقة الشمالية',
            'collector_id' => $collector1->id,
        ]);

        $zone2 = Zone::create([
            'name' => 'المنطقة الجنوبية',
            'collector_id' => $collector2->id,
        ]);

        $zone3 = Zone::create([
            'name' => 'المنطقة الشرقية',
            'collector_id' => null,
        ]);

        // إنشاء المشتركين
        $subscribers = [
            [
                'name' => 'أحمد محمد علي',
                'subscription_no' => '12345',
                'address' => 'شارع الملك فهد، حي النزهة',
                'zone_id' => $zone1->id,
            ],
            [
                'name' => 'فاطمة عبدالله',
                'subscription_no' => '12346',
                'address' => 'شارع الأمير سلطان، حي الروضة',
                'zone_id' => $zone1->id,
            ],
            [
                'name' => 'محمد سعد الدين',
                'subscription_no' => '12347',
                'address' => 'شارع العليا، حي المرسلات',
                'zone_id' => $zone2->id,
            ],
            [
                'name' => 'نورا أحمد',
                'subscription_no' => '12348',
                'address' => 'شارع التحلية، حي السليمانية',
                'zone_id' => $zone2->id,
            ],
            [
                'name' => 'خالد عبدالعزيز',
                'subscription_no' => '12349',
                'address' => 'شارع الملك عبدالعزيز، حي الملز',
                'zone_id' => $zone1->id,
            ],
        ];

        foreach ($subscribers as $subscriberData) {
            Subscriber::create($subscriberData);
        }

        // إنشاء بعض القراءات التجريبية
        $allSubscribers = Subscriber::all();

        foreach ($allSubscribers as $subscriber) {
            // قراءة معتمدة
            $reading1 = Reading::create([
                'subscriber_id' => $subscriber->id,
                'collector_id' => $subscriber->zone->collector_id ?? $collector1->id,
                'previous_reading' => rand(1000, 2000),
                'current_reading' => rand(2001, 3000),
                'status' => 'approved',
                'reviewed_by' => $reviewer1->id,
            ]);

            // إنشاء فاتورة للقراءة المعتمدة
            $consumption = $reading1->current_reading - $reading1->previous_reading;
            $amount = $consumption * 0.257; // سعر الكيلوواط

            Bill::create([
                'reading_id' => $reading1->id,
                'amount' => $amount,
                'issued_at' => now()->subDays(rand(1, 30)),
                'is_paid' => rand(0, 1) == 1,
                'paid_at' => rand(0, 1) == 1 ? now()->subDays(rand(1, 15)) : null,
            ]);

            // قراءة معلقة
            if (rand(0, 1) == 1) {
                Reading::create([
                    'subscriber_id' => $subscriber->id,
                    'collector_id' => $subscriber->zone->collector_id ?? $collector1->id,
                    'previous_reading' => rand(2001, 3000),
                    'current_reading' => rand(3001, 4000),
                    'status' => 'pending',
                ]);
            }

            // قراءة مرفوضة
            if (rand(0, 2) == 1) {
                Reading::create([
                    'subscriber_id' => $subscriber->id,
                    'collector_id' => $subscriber->zone->collector_id ?? $collector2->id,
                    'previous_reading' => rand(1500, 2500),
                    'current_reading' => rand(2501, 3500),
                    'status' => 'rejected',
                    'reviewed_by' => $reviewer2->id,
                    'review_note' => 'القراءة غير واضحة في الصورة',
                ]);
            }
        }

        $this->command->info('تم إنشاء البيانات التجريبية بنجاح!');
        $this->command->info('المستخدمين:');
        $this->command->info('- المدير: admin / password');
        $this->command->info('- المحصل 1: collector1 / password');
        $this->command->info('- المحصل 2: collector2 / password');
        $this->command->info('- المراجع 1: reviewer1 / password');
        $this->command->info('- المراجع 2: reviewer2 / password');
    }
}
