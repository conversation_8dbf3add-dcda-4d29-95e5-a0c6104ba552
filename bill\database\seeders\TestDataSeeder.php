<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Zone;
use App\Models\Subscriber;
use Illuminate\Support\Facades\Hash;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدمين تجريبيين
        $admin = User::firstOrCreate(
            ['username' => 'admin'],
            [
                'name' => 'المدير العام',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'status' => 'active',
            ]
        );

        $collector1 = User::firstOrCreate(
            ['username' => 'collector1'],
            [
                'name' => 'محصل المنطقة الأولى',
                'password' => Hash::make('password'),
                'role' => 'collector',
                'status' => 'active',
                'area' => 'المنطقة الأولى',
            ]
        );

        $collector2 = User::firstOrCreate(
            ['username' => 'collector2'],
            [
                'name' => 'محصل المنطقة الثانية',
                'password' => Hash::make('password'),
                'role' => 'collector',
                'status' => 'active',
                'area' => 'المنطقة الثانية',
            ]
        );

        $reviewer1 = User::firstOrCreate(
            ['username' => 'reviewer1'],
            [
                'name' => 'مراجع المنطقة الأولى',
                'password' => Hash::make('password'),
                'role' => 'reviewer',
                'status' => 'active',
                'area' => 'المنطقة الأولى',
            ]
        );

        $reviewer2 = User::firstOrCreate(
            ['username' => 'reviewer2'],
            [
                'name' => 'مراجع المنطقة الثانية',
                'password' => Hash::make('password'),
                'role' => 'reviewer',
                'status' => 'active',
                'area' => 'المنطقة الثانية',
            ]
        );

        // إنشاء مناطق تجريبية
        $zone1 = Zone::firstOrCreate(
            ['name' => 'المنطقة الأولى'],
            [
                'collector_id' => $collector1->id,
                'reviewer_id' => $reviewer1->id,
                'description' => 'منطقة تجريبية للاختبار',
            ]
        );

        $zone2 = Zone::firstOrCreate(
            ['name' => 'المنطقة الثانية'],
            [
                'collector_id' => $collector2->id,
                'reviewer_id' => $reviewer2->id,
                'description' => 'منطقة تجريبية ثانية للاختبار',
            ]
        );

        // إنشاء مشتركين تجريبيين
        Subscriber::firstOrCreate(
            ['subscription_no' => '001'],
            [
                'name' => 'أحمد محمد علي',
                'address' => 'شارع الملك فهد، حي النزهة',
                'phone' => '0501234567',
                'email' => '<EMAIL>',
                'zone_id' => $zone1->id,
                'meter_number' => 'M001',
                'connection_date' => '2024-01-15',
                'notes' => 'مشترك تجريبي',
            ]
        );

        Subscriber::firstOrCreate(
            ['subscription_no' => '002'],
            [
                'name' => 'فاطمة عبدالله',
                'address' => 'شارع العروبة، حي الصفا',
                'phone' => '0507654321',
                'email' => '<EMAIL>',
                'zone_id' => $zone1->id,
                'meter_number' => 'M002',
                'connection_date' => '2024-02-01',
                'notes' => 'مشتركة تجريبية',
            ]
        );

        Subscriber::firstOrCreate(
            ['subscription_no' => '003'],
            [
                'name' => 'محمد سعد الدين',
                'address' => 'شارع الأمير سلطان، حي المروج',
                'phone' => '0509876543',
                'email' => '<EMAIL>',
                'zone_id' => $zone2->id,
                'meter_number' => 'M003',
                'connection_date' => '2024-01-20',
                'notes' => 'مشترك تجريبي في المنطقة الثانية',
            ]
        );

        $this->command->info('تم إنشاء البيانات التجريبية بنجاح!');
        $this->command->info('المستخدمين:');
        $this->command->info('- admin / password (مدير)');
        $this->command->info('- collector1 / password (محصل)');
        $this->command->info('- collector2 / password (محصل)');
        $this->command->info('- reviewer1 / password (مراجع)');
        $this->command->info('- reviewer2 / password (مراجع)');
    }
}
