["C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Cairo-Regular.ttf", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Cairo-Bold.ttf", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\last_app\\bill\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]