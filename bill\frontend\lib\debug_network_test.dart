import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class NetworkDebugger {
  static Future<void> testConnectivity() async {
    final List<String> testUrls = [
      'http://********:8000/api/user',  // For emulator
      'http://*************:8000/api/user',  // For physical device
      'http://127.0.0.1:8000/api/user',  // Localhost
    ];

    for (String url in testUrls) {
      debugPrint('Testing connectivity to: $url');
      
      try {
        final response = await http.get(
          Uri.parse(url),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        ).timeout(Duration(seconds: 5));

        debugPrint('Response status: ${response.statusCode}');
        debugPrint('Response headers: ${response.headers}');
        
        if (response.body.contains('<html') || response.body.contains('<!DOCTYPE')) {
          debugPrint('WARNING: Received HTML response!');
          debugPrint('HTML preview: ${response.body.substring(0, 200)}...');
        } else {
          debugPrint('Response body: ${response.body}');
        }
        
        debugPrint('---');
      } catch (e) {
        debugPrint('Error connecting to $url: $e');
        debugPrint('---');
      }
    }
  }

  static Future<void> testLogin() async {
    final List<String> baseUrls = [
      'http://********:8000/api',  // For emulator
      'http://*************:8000/api',  // For physical device
    ];

    for (String baseUrl in baseUrls) {
      debugPrint('Testing login to: $baseUrl/login');
      
      try {
        final response = await http.post(
          Uri.parse('$baseUrl/login'),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          body: jsonEncode({
            'username': 'admin',
            'password': 'password',
          }),
        ).timeout(Duration(seconds: 10));

        debugPrint('Login response status: ${response.statusCode}');
        
        if (response.body.contains('<html') || response.body.contains('<!DOCTYPE')) {
          debugPrint('WARNING: Received HTML response instead of JSON!');
          debugPrint('HTML preview: ${response.body.substring(0, 300)}...');
        } else {
          debugPrint('Login response: ${response.body}');
          
          if (response.statusCode == 200) {
            final data = jsonDecode(response.body);
            final token = data['access_token'];
            debugPrint('Login successful! Token: ${token?.substring(0, 20)}...');
            
            // Test authenticated request
            final userResponse = await http.get(
              Uri.parse('$baseUrl/user'),
              headers: {
                'Accept': 'application/json',
                'Authorization': 'Bearer $token',
              },
            );
            
            debugPrint('User endpoint status: ${userResponse.statusCode}');
            debugPrint('User response: ${userResponse.body}');
          }
        }
        
        debugPrint('---');
      } catch (e) {
        debugPrint('Error testing login to $baseUrl: $e');
        debugPrint('---');
      }
    }
  }
}
