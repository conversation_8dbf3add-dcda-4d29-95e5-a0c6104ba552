import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/admin/admin_dashboard_screen.dart';
import 'screens/collector/collector_dashboard_screen.dart';
import 'screens/reviewer/reviewer_dashboard_screen.dart';
import 'screens/connection_test_screen.dart';
import 'services/auth_service.dart';
import 'services/local_db_service.dart';
import 'services/api_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize local database
  await LocalDbService.instance.database;

  runApp(
    MultiProvider(
      providers: [
        // ChangeNotifierProvider expects a ChangeNotifier, if AuthService is not, use Provider
        Provider(create: (context) => AuthService()),
        Provider(create: (context) => ApiService()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'نظام إدارة فواتير الكهرباء',
      debugShowCheckedModeBanner: false,

      // Arabic language configuration
      locale: const Locale('ar', 'SA'),
      supportedLocales: const [
        Locale('ar', 'SA'),
        Locale('en', 'US'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // Theme configuration
      theme: _buildThemeData(),
      darkTheme: _buildDarkThemeData(),
      themeMode: ThemeMode.light,

      // Remove const if the widget is not a const constructor
      home: AuthWrapper(),

      // Routes
      routes: {
        '/login': (context) => LoginScreen(),
        '/admin': (context) => AdminDashboardScreen(),
        '/collector': (context) => CollectorDashboardScreen(),
        '/reviewer': (context) => ReviewerDashboardScreen(),
        '/connection-test': (context) => ConnectionTestScreen(),
      },

      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: child!,
          ),
        );
      },
    );
  }

  ThemeData _buildThemeData() {
    return ThemeData(
      primarySwatch: Colors.blue,
      fontFamily: 'Cairo',
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        displayMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        bodyLarge: TextStyle(fontSize: 16),
        bodyMedium: TextStyle(fontSize: 14),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Cairo',
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue[700],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: const TextStyle(
            fontFamily: 'Cairo',
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      // Use CardThemeData instead of CardTheme
      cardTheme: CardThemeData(
        elevation: 2,
        margin: const EdgeInsets.all(8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16, vertical: 12),
      ),
    );
  }

  ThemeData _buildDarkThemeData() {
    return ThemeData.dark().copyWith(
      primaryColor: Colors.blue[800],
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.blue[900],
        elevation: 2,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue[800],
        ),
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isLoading = true;
  String? _userRole;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      final authService = Provider.of<AuthService>(
        context,
        listen: false,
      );
      final isLoggedIn = await authService.isTokenValid();

      if (isLoggedIn && mounted) {
        final user = await authService.getCurrentUser();
        if (user != null) {
          setState(() {
            _userRole = user['role'];
          });
        }
      }
    } catch (e) {
      debugPrint('Initialization error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء التحميل: ${e.toString()}'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);
    // استخدم await مع isLoggedIn لأنه Future
    return FutureBuilder<bool>(
      future: authService.isLoggedIn,
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return _buildLoadingScreen();
        }
        if (!snapshot.data!) {
          return LoginScreen();

        }
        return _buildRoleBasedScreen();
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'جاري التحميل...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleBasedScreen() {
    switch (_userRole) {
      case 'admin':
        return AdminDashboardScreen();
      case 'collector':
        return CollectorDashboardScreen();
      case 'reviewer':
        return ReviewerDashboardScreen();
      default:
        return LoginScreen();
    }
  }
}
