import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class AddZoneScreen extends StatefulWidget {
  const AddZoneScreen({Key? key}) : super(key: key);

  @override
  _AddZoneScreenState createState() => _AddZoneScreenState();
}

class _AddZoneScreenState extends State<AddZoneScreen> {
  final _formKey = GlobalKey<FormState>();
  final ApiService _apiService = ApiService();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  String? _selectedCollectorId;
  String? _selectedReviewerId;
  bool _isLoading = false;

  List<dynamic> _collectors = [];
  List<dynamic> _reviewers = [];

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      final collectorsResponse = await _apiService.get('/zones/available-collectors');
      final reviewersResponse = await _apiService.get('/zones/available-reviewers');

      debugPrint('Collectors Response: $collectorsResponse');
      debugPrint('Reviewers Response: $reviewersResponse');

      setState(() {
        _collectors = collectorsResponse?['data'] ?? collectorsResponse ?? [];
        _reviewers = reviewersResponse?['data'] ?? reviewersResponse ?? [];
      });

      debugPrint('Collectors loaded: ${_collectors.length}');
      debugPrint('Reviewers loaded: ${_reviewers.length}');
    } catch (e) {
      debugPrint('خطأ في تحميل المستخدمين: $e');
    }
  }

  Future<void> _submitZone() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final zoneData = {
        'name': _nameController.text.trim(),
        'description': _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        'collector_id': _selectedCollectorId,
        'reviewer_id': _selectedReviewerId,
      };

      final result = await _apiService.post('/zones', zoneData);

      if (result != null && mounted) {
        if (result.containsKey('data') || result.containsKey('message')) {
          // Success response
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'تم إنشاء المنطقة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true);
        } else {
          // Error response
          String errorMessage = 'فشل في إنشاء المنطقة';
          if (result.containsKey('message')) {
            errorMessage = result['message'];
          } else if (result.containsKey('errors')) {
            final errors = result['errors'];
            if (errors is Map) {
              errorMessage = errors.values.first.toString();
            }
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
            ),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في إنشاء المنطقة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _resetForm() {
    _formKey.currentState!.reset();
    _nameController.clear();
    _descriptionController.clear();
    setState(() {
      _selectedCollectorId = null;
      _selectedReviewerId = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة منطقة جديدة'),
        backgroundColor: Colors.blue[800],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'بيانات المنطقة',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم المنطقة *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'اسم المنطقة مطلوب';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف المنطقة',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تخصيص المستخدمين',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),

                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المحصل',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCollectorId,
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('لا يوجد محصل'),
                          ),
                          ..._collectors.map((collector) {
                            return DropdownMenuItem<String>(
                              value: collector['id'].toString(),
                              child: Text('${collector['name']} (${collector['username']})'),
                            );
                          }).toList(),
                        ],
                        onChanged: (value) => setState(() => _selectedCollectorId = value),
                      ),
                      const SizedBox(height: 16),

                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'المراجع',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedReviewerId,
                        items: [
                          const DropdownMenuItem<String>(
                            value: null,
                            child: Text('لا يوجد مراجع'),
                          ),
                          ..._reviewers.map((reviewer) {
                            return DropdownMenuItem<String>(
                              value: reviewer['id'].toString(),
                              child: Text('${reviewer['name']} (${reviewer['username']})'),
                            );
                          }).toList(),
                        ],
                        onChanged: (value) => setState(() => _selectedReviewerId = value),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submitZone,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text('إضافة المنطقة'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _resetForm,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
