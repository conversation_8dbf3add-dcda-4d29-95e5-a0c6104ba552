import 'package:flutter/material.dart';
import '../../services/auth_service.dart';
import '../../services/api_service.dart';
import '../auth/login_screen.dart';
import 'zones_management_screen.dart';
import 'subscribers_management_screen.dart';
import 'readings_review_screen.dart';
import 'bills_management_screen.dart';
import 'users_management_screen.dart';
import 'add_user_screen.dart';
import 'reports_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  @override
  _AdminDashboardScreenState createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final AuthService _authService = AuthService();
  final ApiService _apiService = ApiService();

  Map<String, dynamic>? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الإحصائيات
      final zonesResponse = await _apiService.get('/zones');
      final subscribersResponse = await _apiService.get('/subscribers');
      final readingsResponse = await _apiService.get('/readings');
      final billsResponse = await _apiService.get('/bills');
      final pendingReadingsResponse = await _apiService.get('/readings?status=pending');
      final overdueBillsResponse = await _apiService.get('/bills?overdue=1');

      // استخراج البيانات بطريقة آمنة
      final zones = zonesResponse?['data'] ?? zonesResponse ?? [];
      final subscribers = subscribersResponse?['data'] ?? subscribersResponse ?? [];
      final readings = readingsResponse?['data'] ?? readingsResponse ?? [];
      final bills = billsResponse?['data'] ?? billsResponse ?? [];
      final pendingReadings = pendingReadingsResponse?['data'] ?? pendingReadingsResponse ?? [];
      final overdueBills = overdueBillsResponse?['data'] ?? overdueBillsResponse ?? [];

      setState(() {
        _stats = {
          'zones_count': zones.length,
          'subscribers_count': subscribers.length,
          'readings_count': readings.length,
          'bills_count': bills.length,
          'pending_readings': pendingReadings.length,
          'overdue_bills': overdueBills.length,
        };
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _logout() async {
    final success = await _authService.logout();
    if (success && mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => LoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('لوحة تحكم المدير'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  _showProfile();
                  break;
                case 'settings':
                  _showSettings();
                  break;
                case 'logout':
                  _logout();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person, size: 20),
                    SizedBox(width: 8),
                    Text('الملف الشخصي'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 20),
                    SizedBox(width: 8),
                    Text('الإعدادات'),
                  ],
                ),
              ),
              PopupMenuDivider(),
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // بطاقات الإحصائيات
                    _buildStatsCards(),
                    SizedBox(height: 24),

                    // الإجراءات السريعة
                    _buildQuickActions(),
                    SizedBox(height: 24),

                    // التقارير والتنبيهات
                    _buildReportsSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatsCards() {
    if (_stats == null) return SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات العامة',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'المناطق',
              _stats!['zones_count'].toString(),
              Icons.location_on,
              Colors.blue,
            ),
            _buildStatCard(
              'المشتركين',
              _stats!['subscribers_count'].toString(),
              Icons.people,
              Colors.green,
            ),
            _buildStatCard(
              'القراءات',
              _stats!['readings_count'].toString(),
              Icons.assessment,
              Colors.orange,
            ),
            _buildStatCard(
              'الفواتير',
              _stats!['bills_count'].toString(),
              Icons.receipt,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإدارة',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 2,
          children: [
            _buildActionCard(
              'إدارة المستخدمين',
              Icons.people_alt,
              Colors.indigo,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => UsersManagementScreen())),
            ),
            _buildActionCard(
              'إدارة المناطق',
              Icons.location_on,
              Colors.blue,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => ZonesManagementScreen())),
            ),
            _buildActionCard(
              'إدارة المشتركين',
              Icons.people,
              Colors.green,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => SubscribersManagementScreen())),
            ),
            _buildActionCard(
              'مراجعة القراءات',
              Icons.assessment,
              Colors.orange,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => ReadingsReviewScreen())),
            ),
            _buildActionCard(
              'إدارة الفواتير',
              Icons.receipt,
              Colors.purple,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => BillsManagementScreen())),
            ),
            _buildActionCard(
              'التقارير',
              Icons.analytics,
              Colors.teal,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsScreen())),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(icon, color: color, size: 24),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReportsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التنبيهات',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        if (_stats != null) ...[
          if (_stats!['pending_readings'] > 0)
            _buildAlertCard(
              'قراءات معلقة',
              '${_stats!['pending_readings']} قراءة تحتاج للمراجعة',
              Colors.orange,
              Icons.pending_actions,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => ReadingsReviewScreen())),
            ),
          if (_stats!['overdue_bills'] > 0)
            _buildAlertCard(
              'فواتير متأخرة',
              '${_stats!['overdue_bills']} فاتورة متأخرة الدفع',
              Colors.red,
              Icons.warning,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => BillsManagementScreen())),
            ),
          if (_stats!['pending_readings'] == 0 && _stats!['overdue_bills'] == 0)
            Card(
              child: ListTile(
                leading: Icon(Icons.check_circle, color: Colors.green),
                title: Text('لا توجد تنبيهات'),
                subtitle: Text('جميع العمليات تسير بشكل طبيعي'),
              ),
            ),
        ],
      ],
    );
  }

  Widget _buildAlertCard(String title, String subtitle, Color color, IconData icon, VoidCallback onTap) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: color),
        title: Text(title, style: TextStyle(fontWeight: FontWeight.w500)),
        subtitle: Text(subtitle),
        trailing: Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('الملف الشخصي'),
        content: Text('ميزة الملف الشخصي ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('الإعدادات'),
        content: Text('ميزة الإعدادات ستكون متاحة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('موافق'),
          ),
        ],
      ),
    );
  }
}
