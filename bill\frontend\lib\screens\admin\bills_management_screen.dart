import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class BillsManagementScreen extends StatefulWidget {
  @override
  _BillsManagementScreenState createState() => _BillsManagementScreenState();
}

class _BillsManagementScreenState extends State<BillsManagementScreen> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  late TabController _tabController;
  
  List<dynamic> _allBills = [];
  List<dynamic> _paidBills = [];
  List<dynamic> _unpaidBills = [];
  List<dynamic> _overdueBills = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final allBills = await _apiService.getBills();
      final overdueBills = await _apiService.getBills(overdue: true);
      
      if (allBills != null) {
        setState(() {
          _allBills = allBills;
          _paidBills = allBills.where((b) => b['is_paid'] == true).toList();
          _unpaidBills = allBills.where((b) => b['is_paid'] == false).toList();
          _overdueBills = overdueBills ?? [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إدارة الفواتير'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'generate':
                  _generateBills();
                  break;
                case 'export':
                  _exportBills();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'generate',
                child: Row(
                  children: [
                    Icon(Icons.auto_awesome, size: 20),
                    SizedBox(width: 8),
                    Text('توليد فواتير تلقائياً'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download, size: 20),
                    SizedBox(width: 8),
                    Text('تصدير التقرير'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              text: 'الكل',
              icon: Badge(
                label: Text(_allBills.length.toString()),
                child: Icon(Icons.receipt_long),
              ),
            ),
            Tab(
              text: 'مدفوعة',
              icon: Badge(
                label: Text(_paidBills.length.toString()),
                child: Icon(Icons.check_circle),
              ),
            ),
            Tab(
              text: 'غير مدفوعة',
              icon: Badge(
                label: Text(_unpaidBills.length.toString()),
                child: Icon(Icons.pending),
              ),
            ),
            Tab(
              text: 'متأخرة',
              icon: Badge(
                label: Text(_overdueBills.length.toString()),
                child: Icon(Icons.warning),
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildBillsList(_allBills, 'all'),
                _buildBillsList(_paidBills, 'paid'),
                _buildBillsList(_unpaidBills, 'unpaid'),
                _buildBillsList(_overdueBills, 'overdue'),
              ],
            ),
    );
  }

  Widget _buildBillsList(List<dynamic> bills, String type) {
    if (bills.isEmpty) {
      return _buildEmptyState(type);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: bills.length,
        itemBuilder: (context, index) {
          final bill = bills[index];
          return _buildBillCard(bill);
        },
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    String message;
    IconData icon;
    
    switch (type) {
      case 'paid':
        message = 'لا توجد فواتير مدفوعة';
        icon = Icons.payment;
        break;
      case 'unpaid':
        message = 'لا توجد فواتير غير مدفوعة';
        icon = Icons.payment_outlined;
        break;
      case 'overdue':
        message = 'لا توجد فواتير متأخرة';
        icon = Icons.schedule;
        break;
      default:
        message = 'لا توجد فواتير';
        icon = Icons.receipt_long;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillCard(Map<String, dynamic> bill) {
    final isPaid = bill['is_paid'] == true;
    final amount = double.tryParse(bill['amount'].toString()) ?? 0.0;
    final isOverdue = _isOverdue(bill);

    Color statusColor = isPaid ? Colors.green : (isOverdue ? Colors.red : Colors.orange);
    String statusText = isPaid ? 'مدفوعة' : (isOverdue ? 'متأخرة' : 'غير مدفوعة');
    IconData statusIcon = isPaid ? Icons.check_circle : (isOverdue ? Icons.warning : Icons.pending);

    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 3,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        bill['reading']?['subscriber']?['name'] ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'رقم الاشتراك: ${bill['reading']?['subscriber']?['subscription_no'] ?? ''}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: statusColor),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Amount
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.attach_money, color: Colors.blue[700]),
                  SizedBox(width: 8),
                  Text(
                    'المبلغ: ${amount.toStringAsFixed(2)} ريال',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 12),
            
            // Details
            _buildInfoRow('تاريخ الإصدار', _formatDate(bill['issued_at'])),
            if (isPaid && bill['paid_at'] != null)
              _buildInfoRow('تاريخ الدفع', _formatDate(bill['paid_at'])),
            
            // Reading details
            if (bill['reading'] != null) ...[
              SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'القراءة السابقة: ${bill['reading']['previous_reading']}',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'القراءة الحالية: ${bill['reading']['current_reading']}',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                ],
              ),
              Text(
                'الاستهلاك: ${(bill['reading']['current_reading'] - bill['reading']['previous_reading']).toStringAsFixed(2)} كيلوواط',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
            
            // Actions
            if (!isPaid) ...[
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _markAsPaid(bill),
                      icon: Icon(Icons.payment, size: 16),
                      label: Text('تحديد كمدفوعة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _showBillDetails(bill),
                    icon: Icon(Icons.info, size: 16),
                    label: Text('التفاصيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ] else ...[
              SizedBox(height: 16),
              Center(
                child: ElevatedButton.icon(
                  onPressed: () => _showBillDetails(bill),
                  icon: Icon(Icons.info, size: 16),
                  label: Text('عرض التفاصيل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _isOverdue(Map<String, dynamic> bill) {
    if (bill['is_paid'] == true) return false;
    
    try {
      final issuedAt = DateTime.parse(bill['issued_at']);
      final now = DateTime.now();
      return now.difference(issuedAt).inDays > 30;
    } catch (e) {
      return false;
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  void _showBillDetails(Map<String, dynamic> bill) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الفاتورة'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('رقم الفاتورة', bill['id'].toString()),
              _buildDetailRow('اسم المشترك', bill['reading']?['subscriber']?['name'] ?? ''),
              _buildDetailRow('رقم الاشتراك', bill['reading']?['subscriber']?['subscription_no'] ?? ''),
              _buildDetailRow('المبلغ', '${bill['amount']} ريال'),
              _buildDetailRow('تاريخ الإصدار', _formatDate(bill['issued_at'])),
              _buildDetailRow('الحالة', bill['is_paid'] == true ? 'مدفوعة' : 'غير مدفوعة'),
              if (bill['is_paid'] == true && bill['paid_at'] != null)
                _buildDetailRow('تاريخ الدفع', _formatDate(bill['paid_at'])),
              if (bill['reading'] != null) ...[
                Divider(),
                Text('تفاصيل القراءة:', style: TextStyle(fontWeight: FontWeight.bold)),
                _buildDetailRow('القراءة السابقة', bill['reading']['previous_reading'].toString()),
                _buildDetailRow('القراءة الحالية', bill['reading']['current_reading'].toString()),
                _buildDetailRow('الاستهلاك', '${(bill['reading']['current_reading'] - bill['reading']['previous_reading']).toStringAsFixed(2)} كيلوواط'),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _markAsPaid(Map<String, dynamic> bill) async {
    try {
      final result = await _apiService.markBillAsPaid(bill['id']);
      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تحديد الفاتورة كمدفوعة')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحديث حالة الفاتورة')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحديث الفاتورة: $e')),
      );
    }
  }

  Future<void> _generateBills() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('توليد فواتير تلقائياً'),
        content: Text('هل تريد توليد فواتير لجميع القراءات المعتمدة التي لا تحتوي على فواتير؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                final result = await _apiService.generateBills();
                if (result != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(result['message'] ?? 'تم توليد الفواتير بنجاح')),
                  );
                  _loadData();
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('فشل في توليد الفواتير')),
                  );
                }
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('خطأ في توليد الفواتير: $e')),
                );
              }
            },
            child: Text('توليد'),
          ),
        ],
      ),
    );
  }

  void _exportBills() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('ميزة تصدير التقارير ستكون متاحة قريباً')),
    );
  }
}