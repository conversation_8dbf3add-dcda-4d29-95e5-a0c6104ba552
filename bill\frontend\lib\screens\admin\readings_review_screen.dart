import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class ReadingsReviewScreen extends StatefulWidget {
  @override
  _ReadingsReviewScreenState createState() => _ReadingsReviewScreenState();
}

class _ReadingsReviewScreenState extends State<ReadingsReviewScreen> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  late TabController _tabController;

  List<dynamic> _pendingReadings = [];
  List<dynamic> _approvedReadings = [];
  List<dynamic> _rejectedReadings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final readingsResponse = await _apiService.get('/readings');

      debugPrint('Readings Review Response: $readingsResponse');
      debugPrint('Response Type: ${readingsResponse.runtimeType}');

      List<dynamic> allReadings = [];

      if (readingsResponse != null) {
        if (readingsResponse is Map && readingsResponse.containsKey('data')) {
          allReadings = readingsResponse['data'] ?? [];
        } else if (readingsResponse is List) {
          allReadings = readingsResponse;
        }
      }

      setState(() {
        _pendingReadings = allReadings.where((r) => r['status'] == 'pending').toList();
        _approvedReadings = allReadings.where((r) => r['status'] == 'approved').toList();
        _rejectedReadings = allReadings.where((r) => r['status'] == 'rejected').toList();
        _isLoading = false;
      });

      debugPrint('Readings Review loaded - Pending: ${_pendingReadings.length}, Approved: ${_approvedReadings.length}, Rejected: ${_rejectedReadings.length}');
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('مراجعة القراءات'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: 'معلقة',
              icon: Badge(
                label: Text(_pendingReadings.length.toString()),
                child: Icon(Icons.pending_actions),
              ),
            ),
            Tab(
              text: 'معتمدة',
              icon: Badge(
                label: Text(_approvedReadings.length.toString()),
                child: Icon(Icons.check_circle),
              ),
            ),
            Tab(
              text: 'مرفوضة',
              icon: Badge(
                label: Text(_rejectedReadings.length.toString()),
                child: Icon(Icons.cancel),
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildReadingsList(_pendingReadings, 'pending'),
                _buildReadingsList(_approvedReadings, 'approved'),
                _buildReadingsList(_rejectedReadings, 'rejected'),
              ],
            ),
    );
  }

  Widget _buildReadingsList(List<dynamic> readings, String status) {
    if (readings.isEmpty) {
      return _buildEmptyState(status);
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: readings.length,
        itemBuilder: (context, index) {
          final reading = readings[index];
          return _buildReadingCard(reading, status);
        },
      ),
    );
  }

  Widget _buildEmptyState(String status) {
    String message;
    IconData icon;

    switch (status) {
      case 'pending':
        message = 'لا توجد قراءات معلقة';
        icon = Icons.check_circle_outline;
        break;
      case 'approved':
        message = 'لا توجد قراءات معتمدة';
        icon = Icons.approval;
        break;
      case 'rejected':
        message = 'لا توجد قراءات مرفوضة';
        icon = Icons.block;
        break;
      default:
        message = 'لا توجد قراءات';
        icon = Icons.assessment;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingCard(Map<String, dynamic> reading, String status) {
    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case 'pending':
        statusColor = Colors.orange;
        statusIcon = Icons.pending_actions;
        break;
      case 'approved':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help;
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with subscriber info and status
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reading['subscriber']?['name'] ?? '',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'رقم الاشتراك: ${reading['subscriber']?['subscription_no'] ?? ''}',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: statusColor),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      SizedBox(width: 4),
                      Text(
                        _getStatusText(status),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Reading details
            Row(
              children: [
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة السابقة',
                    reading['previous_reading'].toString(),
                    Colors.grey[600]!,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة الحالية',
                    reading['current_reading'].toString(),
                    Colors.blue[700]!,
                  ),
                ),
              ],
            ),

            SizedBox(height: 12),

            // Consumption
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.flash_on, color: Colors.green[700]),
                  SizedBox(width: 8),
                  Text(
                    'الاستهلاك: ${_calculateConsumption(reading).toStringAsFixed(2)} كيلوواط',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.green[700],
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 12),

            // Additional info
            _buildInfoRow('المحصل', reading['collector']?['username'] ?? ''),
            _buildInfoRow('التاريخ', _formatDate(reading['created_at'])),

            if (reading['reviewer'] != null)
              _buildInfoRow('المراجع', reading['reviewer']['username']),

            if (reading['review_note'] != null) ...[
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.note, size: 16, color: Colors.grey[600]),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'ملاحظة: ${reading['review_note']}',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // Image if available
            if (reading['image_path'] != null) ...[
              SizedBox(height: 12),
              Text(
                'صورة العداد:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Container(
                height: 150,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    'http://localhost:8000/storage/${reading['image_path']}',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.broken_image, size: 32, color: Colors.grey[400]),
                              Text('لا يمكن تحميل الصورة', style: TextStyle(color: Colors.grey[600])),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],

            // Action buttons for pending readings
            if (status == 'pending') ...[
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _approveReading(reading),
                      icon: Icon(Icons.check),
                      label: Text('اعتماد'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _showRejectDialog(reading),
                      icon: Icon(Icons.close),
                      label: Text('رفض'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReadingInfo(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'approved':
        return 'معتمدة';
      case 'rejected':
        return 'مرفوضة';
      default:
        return status;
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  void _showRejectDialog(Map<String, dynamic> reading) {
    final _noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('ر��ض القراءة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سبب رفض قراءة ${reading['subscriber']?['name'] ?? ''}:'),
            SizedBox(height: 16),
            TextFormField(
              controller: _noteController,
              decoration: InputDecoration(
                labelText: 'سبب الرفض',
                border: OutlineInputBorder(),
                hintText: 'اكتب سبب رفض هذه القراءة...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _rejectReading(reading, _noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('رفض'),
          ),
        ],
      ),
    );
  }

  Future<void> _approveReading(Map<String, dynamic> reading) async {
    try {
      final result = await _apiService.approveReading(reading['id'], null);
      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم اعتماد القراءة بنجاح')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في اعتماد القراءة')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في اعتماد القراءة: $e')),
      );
    }
  }

  Future<void> _rejectReading(Map<String, dynamic> reading, String note) async {
    if (note.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى كتابة سبب الرفض')),
      );
      return;
    }

    try {
      final result = await _apiService.rejectReading(reading['id'], note);
      if (result != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم رفض القراءة')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في رفض القراءة')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في رفض القراءة: $e')),
      );
    }
  }

  double _calculateConsumption(Map<String, dynamic> reading) {
    final currentReading = double.tryParse(reading['current_reading'].toString()) ?? 0.0;
    final previousReading = double.tryParse(reading['previous_reading'].toString()) ?? 0.0;
    return currentReading - previousReading;
  }
}
