import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class ReportsScreen extends StatefulWidget {
  @override
  _ReportsScreenState createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final ApiService _apiService = ApiService();
  
  Map<String, dynamic>? _reportData;
  bool _isLoading = true;
  DateTime _selectedMonth = DateTime.now();

  @override
  void initState() {
    super.initState();
    _loadReportData();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load various data for reports
      final zones = await _apiService.getZones();
      final subscribers = await _apiService.getSubscribers();
      final readings = await _apiService.getReadings();
      final bills = await _apiService.getBills();
      final overdueBills = await _apiService.getBills(overdue: true);

      // Calculate statistics
      final totalRevenue = bills?.fold<double>(0.0, (sum, bill) {
        if (bill['is_paid'] == true) {
          return sum + (double.tryParse(bill['amount'].toString()) ?? 0.0);
        }
        return sum;
      }) ?? 0.0;

      final pendingRevenue = bills?.fold<double>(0.0, (sum, bill) {
        if (bill['is_paid'] == false) {
          return sum + (double.tryParse(bill['amount'].toString()) ?? 0.0);
        }
        return sum;
      }) ?? 0.0;

      final totalConsumption = readings?.fold<double>(0.0, (sum, reading) {
        if (reading['status'] == 'approved') {
          return sum + (reading['current_reading'] - reading['previous_reading']);
        }
        return sum;
      }) ?? 0.0;

      setState(() {
        _reportData = {
          'zones_count': zones?.length ?? 0,
          'subscribers_count': subscribers?.length ?? 0,
          'readings_count': readings?.length ?? 0,
          'bills_count': bills?.length ?? 0,
          'overdue_bills_count': overdueBills?.length ?? 0,
          'total_revenue': totalRevenue,
          'pending_revenue': pendingRevenue,
          'total_consumption': totalConsumption,
          'collection_rate': bills?.isNotEmpty == true 
              ? (bills!.where((b) => b['is_paid'] == true).length / bills!.length * 100)
              : 0.0,
        };
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('التقارير والإحصائيات'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadReportData,
          ),
          IconButton(
            icon: Icon(Icons.download),
            onPressed: _exportReport,
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadReportData,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Month selector
                    _buildMonthSelector(),
                    SizedBox(height: 24),
                    
                    // Financial summary
                    _buildFinancialSummary(),
                    SizedBox(height: 24),
                    
                    // Operational statistics
                    _buildOperationalStats(),
                    SizedBox(height: 24),
                    
                    // Performance metrics
                    _buildPerformanceMetrics(),
                    SizedBox(height: 24),
                    
                    // Charts placeholder
                    _buildChartsSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildMonthSelector() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.calendar_month, color: Colors.blue[700]),
            SizedBox(width: 12),
            Text(
              'تقرير شهر:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _selectMonth,
                child: Text(
                  '${_getMonthName(_selectedMonth.month)} ${_selectedMonth.year}',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummary() {
    if (_reportData == null) return SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الملخص المالي',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildFinancialCard(
              'الإيرادات المحصلة',
              '${_reportData!['total_revenue'].toStringAsFixed(2)} ريال',
              Icons.attach_money,
              Colors.green,
            ),
            _buildFinancialCard(
              'الإيرادات المعلقة',
              '${_reportData!['pending_revenue'].toStringAsFixed(2)} ريال',
              Icons.pending,
              Colors.orange,
            ),
            _buildFinancialCard(
              'معدل التحصيل',
              '${_reportData!['collection_rate'].toStringAsFixed(1)}%',
              Icons.trending_up,
              Colors.blue,
            ),
            _buildFinancialCard(
              'الفواتير المتأخرة',
              _reportData!['overdue_bills_count'].toString(),
              Icons.warning,
              Colors.red,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinancialCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationalStats() {
    if (_reportData == null) return SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات التشغيلية',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي المناطق',
              _reportData!['zones_count'].toString(),
              Icons.location_on,
              Colors.blue,
            ),
            _buildStatCard(
              'إجمالي المشتركين',
              _reportData!['subscribers_count'].toString(),
              Icons.people,
              Colors.green,
            ),
            _buildStatCard(
              'إجمالي القراءات',
              _reportData!['readings_count'].toString(),
              Icons.assessment,
              Colors.orange,
            ),
            _buildStatCard(
              'إجمالي الاستهلاك',
              '${_reportData!['total_consumption'].toStringAsFixed(0)} كيلوواط',
              Icons.flash_on,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مؤشرات الأداء',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        Card(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                _buildMetricRow(
                  'متوسط الاستهلاك الشهري',
                  '${(_reportData?['total_consumption'] ?? 0) / (_reportData?['subscribers_count'] ?? 1)} كيلوواط',
                  Icons.trending_up,
                ),
                Divider(),
                _buildMetricRow(
                  'متوسط قيمة الفاتورة',
                  '${(_reportData?['total_revenue'] ?? 0) / (_reportData?['bills_count'] ?? 1)} ريال',
                  Icons.receipt,
                ),
                Divider(),
                _buildMetricRow(
                  'معدل نمو المشتركين',
                  '+5.2%', // This would be calculated from historical data
                  Icons.people_alt,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricRow(String title, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue[700]),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الرسوم البيانية',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        Card(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bar_chart,
                          size: 48,
                          color: Colors.grey[400],
                        ),
                        SizedBox(height: 8),
                        Text(
                          'الرسوم البيانية ستكون متاحة قريباً',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'رسم بياني للإيرادات الشهرية والاستهلاك',
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _getMonthName(int month) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[month - 1];
  }

  Future<void> _selectMonth() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedMonth,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDatePickerMode: DatePickerMode.year,
    );
    
    if (picked != null && picked != _selectedMonth) {
      setState(() {
        _selectedMonth = picked;
      });
      _loadReportData();
    }
  }

  void _exportReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تصدير التقرير'),
        content: Text('اختر تنسيق التصدير:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportToPDF();
            },
            child: Text('PDF'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportToExcel();
            },
            child: Text('Excel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('ميزة تصدير PDF ستكون متاحة قريباً')),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('ميزة تصدير Excel ستكون متاحة قريباً')),
    );
  }
}