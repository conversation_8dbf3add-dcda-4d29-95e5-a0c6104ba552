import 'package:flutter/material.dart';
import '../../services/api_service.dart';
import 'add_subscriber_screen.dart';

class SubscribersManagementScreen extends StatefulWidget {
  @override
  _SubscribersManagementScreenState createState() => _SubscribersManagementScreenState();
}

class _SubscribersManagementScreenState extends State<SubscribersManagementScreen> {
  final ApiService _apiService = ApiService();
  List<dynamic> _subscribers = [];
  List<dynamic> _zones = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final subscribers = await _apiService.getSubscribers();
      final zones = await _apiService.getZones();

      setState(() {
        _subscribers = subscribers ?? [];
        _zones = zones ?? [];
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<dynamic> get _filteredSubscribers {
    if (_searchQuery.isEmpty) return _subscribers;

    return _subscribers.where((subscriber) {
      final name = subscriber['name']?.toString().toLowerCase() ?? '';
      final subscriptionNo = subscriber['subscription_no']?.toString().toLowerCase() ?? '';
      final address = subscriber['address']?.toString().toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();

      return name.contains(query) ||
             subscriptionNo.contains(query) ||
             address.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إدارة المشتركين'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(60),
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'البحث في المشتركين...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: _filteredSubscribers.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: EdgeInsets.all(16),
                      itemCount: _filteredSubscribers.length,
                      itemBuilder: (context, index) {
                        final subscriber = _filteredSubscribers[index];
                        return _buildSubscriberCard(subscriber);
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AddSubscriberScreen()),
          );
          if (result == true) {
            _loadData(); // Refresh the list if subscriber was added
          }
        },
        child: Icon(Icons.person_add),
        tooltip: 'إضافة مشترك جديد',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _searchQuery.isEmpty ? Icons.people_outline : Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty ? 'لا يوجد مشتركين' : 'لا توجد نتائج للبحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty
                ? 'اضغط على زر + لإضافة مشترك جديد'
                : 'جرب البحث بكلمات مختلفة',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriberCard(Map<String, dynamic> subscriber) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: Colors.green[100],
          child: Icon(Icons.person, color: Colors.green[800]),
        ),
        title: Text(
          subscriber['name'] ?? '',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text('رقم الاشتراك: ${subscriber['subscription_no'] ?? ''}'),
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('العنوان', subscriber['address'] ?? ''),
                _buildInfoRow('المنطقة', subscriber['zone']?['name'] ?? 'غير محدد'),
                if (subscriber['zone']?['collector'] != null)
                  _buildInfoRow('المحصل', subscriber['zone']['collector']['username']),
                SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _showEditSubscriberDialog(subscriber),
                      icon: Icon(Icons.edit, size: 16),
                      label: Text('تعديل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _showSubscriberDetails(subscriber),
                      icon: Icon(Icons.info, size: 16),
                      label: Text('التفاصيل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _showDeleteConfirmation(subscriber),
                      icon: Icon(Icons.delete, size: 16),
                      label: Text('حذف'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showAddSubscriberDialog() {
    final _nameController = TextEditingController();
    final _subscriptionNoController = TextEditingController();
    final _addressController = TextEditingController();
    int? _selectedZoneId;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('إضافة مشترك جديد'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم المشترك',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16),
                TextFormField(
                  controller: _subscriptionNoController,
                  decoration: InputDecoration(
                    labelText: 'رقم الاشتراك',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16),
                TextFormField(
                  controller: _addressController,
                  decoration: InputDecoration(
                    labelText: 'العنوان',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 16),
                DropdownButtonFormField<int>(
                  value: _selectedZoneId,
                  decoration: InputDecoration(
                    labelText: 'المنطقة',
                    border: OutlineInputBorder(),
                  ),
                  items: _zones.map((zone) => DropdownMenuItem<int>(
                    value: zone['id'],
                    child: Text(zone['name']),
                  )).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedZoneId = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _addSubscriber(
                _nameController.text,
                _subscriptionNoController.text,
                _addressController.text,
                _selectedZoneId,
              ),
              child: Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditSubscriberDialog(Map<String, dynamic> subscriber) {
    final _nameController = TextEditingController(text: subscriber['name']);
    final _subscriptionNoController = TextEditingController(text: subscriber['subscription_no']);
    final _addressController = TextEditingController(text: subscriber['address']);
    int? _selectedZoneId = subscriber['zone_id'];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('تعديل المشترك'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'اسم المشترك',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16),
                TextFormField(
                  controller: _subscriptionNoController,
                  decoration: InputDecoration(
                    labelText: 'رقم الاشتراك',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 16),
                TextFormField(
                  controller: _addressController,
                  decoration: InputDecoration(
                    labelText: 'العنوان',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                SizedBox(height: 16),
                DropdownButtonFormField<int>(
                  value: _selectedZoneId,
                  decoration: InputDecoration(
                    labelText: 'المنطقة',
                    border: OutlineInputBorder(),
                  ),
                  items: _zones.map((zone) => DropdownMenuItem<int>(
                    value: zone['id'],
                    child: Text(zone['name']),
                  )).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedZoneId = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _updateSubscriber(
                subscriber['id'],
                _nameController.text,
                _subscriptionNoController.text,
                _addressController.text,
                _selectedZoneId,
              ),
              child: Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  void _showSubscriberDetails(Map<String, dynamic> subscriber) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل المشترك'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('الاسم', subscriber['name'] ?? ''),
              _buildDetailRow('رقم الاشتراك', subscriber['subscription_no'] ?? ''),
              _buildDetailRow('العنوان', subscriber['address'] ?? ''),
              _buildDetailRow('المنطقة', subscriber['zone']?['name'] ?? 'غير محدد'),
              if (subscriber['zone']?['collector'] != null)
                _buildDetailRow('المحصل', subscriber['zone']['collector']['username']),
              _buildDetailRow('تاريخ الإنشاء', _formatDate(subscriber['created_at'])),
              _buildDetailRow('آخر تحديث', _formatDate(subscriber['updated_at'])),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  void _showDeleteConfirmation(Map<String, dynamic> subscriber) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المشترك "${subscriber['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _deleteSubscriber(subscriber['id']),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _addSubscriber(String name, String subscriptionNo, String address, int? zoneId) async {
    if (name.trim().isEmpty || subscriptionNo.trim().isEmpty || address.trim().isEmpty || zoneId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى إدخال جميع البيانات المطلوبة')),
      );
      return;
    }

    try {
      final result = await _apiService.createSubscriber({
        'name': name.trim(),
        'subscription_no': subscriptionNo.trim(),
        'address': address.trim(),
        'zone_id': zoneId,
      });

      if (result != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم إضافة المشترك بنجاح')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في إضا��ة المشترك')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إضافة المشترك: $e')),
      );
    }
  }

  Future<void> _updateSubscriber(int subscriberId, String name, String subscriptionNo, String address, int? zoneId) async {
    if (name.trim().isEmpty || subscriptionNo.trim().isEmpty || address.trim().isEmpty || zoneId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى إدخال جميع البيانات المطلوبة')),
      );
      return;
    }

    try {
      final result = await _apiService.updateSubscriber(subscriberId, {
        'name': name.trim(),
        'subscription_no': subscriptionNo.trim(),
        'address': address.trim(),
        'zone_id': zoneId,
      });

      if (result != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تحديث المشترك بنجاح')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحديث المشترك')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحديث المشترك: $e')),
      );
    }
  }

  Future<void> _deleteSubscriber(int subscriberId) async {
    try {
      final success = await _apiService.deleteSubscriber(subscriberId);
      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف المشترك بنجاح')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف المشترك')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في حذف المشترك: $e')),
      );
    }
  }
}
