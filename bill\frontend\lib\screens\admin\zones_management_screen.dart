import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class ZonesManagementScreen extends StatefulWidget {
  @override
  _ZonesManagementScreenState createState() => _ZonesManagementScreenState();
}

class _ZonesManagementScreenState extends State<ZonesManagementScreen> {
  final ApiService _apiService = ApiService();
  List<dynamic> _zones = [];
  List<dynamic> _collectors = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final zones = await _apiService.getZones();
      // TODO: Add API endpoint to get collectors
      // final collectors = await _apiService.getCollectors();

      setState(() {
        _zones = zones ?? [];
        // _collectors = collectors ?? [];
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إدارة المناطق'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: _zones.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: EdgeInsets.all(16),
                      itemCount: _zones.length,
                      itemBuilder: (context, index) {
                        final zone = _zones[index];
                        return _buildZoneCard(zone);
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddZoneDialog,
        child: Icon(Icons.add),
        tooltip: 'إضافة منطقة جديدة',
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد مناطق',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة منطقة جديدة',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildZoneCard(Map<String, dynamic> zone) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue[100],
          child: Icon(Icons.location_on, color: Colors.blue[800]),
        ),
        title: Text(
          zone['name'] ?? '',
          style: TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (zone['collector'] != null)
              Text('المحصل: ${zone['collector']['username']}')
            else
              Text('لا يوجد محصل مخصص', style: TextStyle(color: Colors.orange)),
            Text('عدد المشتركين: ${zone['subscribers']?.length ?? 0}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditZoneDialog(zone);
                break;
              case 'delete':
                _showDeleteConfirmation(zone);
                break;
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  void _showAddZoneDialog() {
    final _nameController = TextEditingController();
    int? _selectedCollectorId;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('إضافة منطقة جديدة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'اسم المنطقة',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              DropdownButtonFormField<int>(
                value: _selectedCollectorId,
                decoration: InputDecoration(
                  labelText: 'المحصل',
                  border: OutlineInputBorder(),
                ),
                items: [
                  DropdownMenuItem<int>(
                    value: null,
                    child: Text('بدون محصل'),
                  ),
                  ..._collectors.map((collector) => DropdownMenuItem<int>(
                    value: collector['id'],
                    child: Text(collector['username']),
                  )).toList(),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedCollectorId = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _addZone(_nameController.text, _selectedCollectorId),
              child: Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showEditZoneDialog(Map<String, dynamic> zone) {
    final _nameController = TextEditingController(text: zone['name']);
    int? _selectedCollectorId = zone['collector_id'];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('تعديل المنطقة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'اسم المنط��ة',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              DropdownButtonFormField<int>(
                value: _selectedCollectorId,
                decoration: InputDecoration(
                  labelText: 'المحصل',
                  border: OutlineInputBorder(),
                ),
                items: [
                  DropdownMenuItem<int>(
                    value: null,
                    child: Text('بدون محصل'),
                  ),
                  ..._collectors.map((collector) => DropdownMenuItem<int>(
                    value: collector['id'],
                    child: Text(collector['username']),
                  )).toList(),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedCollectorId = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => _updateZone(zone['id'], _nameController.text, _selectedCollectorId),
              child: Text('حفظ'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> zone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف منطقة "${zone['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _deleteZone(zone['id']),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _addZone(String name, int? collectorId) async {
    if (name.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى إدخال اسم المنطقة')),
      );
      return;
    }

    try {
      final result = await _apiService.createZone(name.trim(), collectorId);
      if (result != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم إضافة المنطقة بنجاح')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في إضافة المنطقة')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إضافة المنطقة: $e')),
      );
    }
  }

  Future<void> _updateZone(int zoneId, String name, int? collectorId) async {
    if (name.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى إدخال اسم المنطقة')),
      );
      return;
    }

    try {
      final result = await _apiService.put('/zones/$zoneId', {
        'name': name.trim(),
        'collector_id': collectorId,
      });
      
      if (result != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم تحديث المنطقة بنجاح')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحديث المنطقة')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تحديث المنطقة: $e')),
      );
    }
  }

  Future<void> _deleteZone(int zoneId) async {
    try {
      final success = await _apiService.delete('/zones/$zoneId');
      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف المنطقة بنجاح')),
        );
        _loadData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في حذف المنطقة')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في حذف المنطقة: $e')),
      );
    }
  }
}