import 'package:flutter/material.dart';
import '../services/api_service.dart';
import 'login_screen.dart';
import 'readings_screen.dart';
import 'bills_screen.dart';
import 'subscribers_screen.dart';
import 'zones_screen.dart';

class ArabicDashboardScreen extends StatefulWidget {
  const ArabicDashboardScreen({super.key});

  @override
  State<ArabicDashboardScreen> createState() => _ArabicDashboardScreenState();
}

class _ArabicDashboardScreenState extends State<ArabicDashboardScreen> {
  final _apiService = ApiService();
  Map<String, dynamic>? _user;
  List<dynamic> _bills = [];
  List<dynamic> _subscribers = [];
  List<dynamic> _zones = [];
  List<dynamic> _pendingReadings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final user = await _apiService.getCurrentUser();
      final bills = await _apiService.getBills();
      final subscribers = await _apiService.getSubscribers();
      final zones = await _apiService.getZones();
      final pendingReadings = await _apiService.getPendingReadings();

      setState(() {
        _user = user;
        _bills = bills;
        _subscribers = subscribers;
        _zones = zones;
        _pendingReadings = pendingReadings;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: $e')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _logout() async {
    try {
      await _apiService.logout();
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تسجيل الخروج: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('نظام إدارة الفواتير'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          actions: [
            IconButton(
              onPressed: _logout,
              icon: const Icon(Icons.logout),
              tooltip: 'تسجيل الخروج',
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رسالة الترحيب
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'مرحباً، ${_user?['name'] ?? 'المستخدم'}!',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text('البريد الإلكترون��: ${_user?['email'] ?? ''}'),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // بطاقات الإحصائيات
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'إجمالي الفواتير',
                            _bills.length.toString(),
                            Icons.receipt,
                            Colors.blue,
                            _navigateToBills,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            'المشتركين',
                            _subscribers.length.toString(),
                            Icons.people,
                            Colors.green,
                            _navigateToSubscribers,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'المناطق',
                            _zones.length.toString(),
                            Icons.location_on,
                            Colors.orange,
                            _navigateToZones,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            'القراءات المعلقة',
                            _pendingReadings.length.toString(),
                            Icons.pending,
                            Colors.red,
                            _navigateToReadings,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // القراءات المعلقة
                    if (_pendingReadings.isNotEmpty) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'القراءات في انتظار الموافقة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: _navigateToReadings,
                            child: const Text('عرض الكل'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _pendingReadings.length > 3 ? 3 : _pendingReadings.length,
                        itemBuilder: (context, index) {
                          final reading = _pendingReadings[index];
                          return Card(
                            child: ListTile(
                              leading: const Icon(
                                Icons.pending_actions,
                                color: Colors.orange,
                              ),
                              title: Text('قراءة #${reading['id']}'),
                              subtitle: Text(
                                'المشترك: ${reading['subscriber']?['name'] ?? 'غير محدد'}\n'
                                'القرا��ة الحالية: ${reading['current_reading']}\n'
                                'الاستهلاك: ${reading['units_consumed']} وحدة',
                              ),
                              isThreeLine: true,
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.check, color: Colors.green),
                                    onPressed: () => _approveReading(reading['id']),
                                    tooltip: 'موافقة',
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.close, color: Colors.red),
                                    onPressed: () => _rejectReading(reading['id']),
                                    tooltip: 'رفض',
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                    ],

                    // الفواتير الحديثة
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'الفواتير الحديثة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton(
                          onPressed: _navigateToBills,
                          child: const Text('عرض الكل'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _bills.isEmpty
                        ? const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Text('لا توجد فواتير'),
                            ),
                          )
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _bills.length > 5 ? 5 : _bills.length,
                            itemBuilder: (context, index) {
                              final bill = _bills[index];
                              return Card(
                                child: ListTile(
                                  leading: Icon(
                                    Icons.receipt,
                                    color: _getStatusColor(bill['status']),
                                  ),
                                  title: Text('فاتورة #${bill['bill_number'] ?? bill['id']}'),
                                  subtitle: Text(
                                    'المبلغ: ${bill['amount']} ريال\n'
                                    'الحالة: ${_getStatusText(bill['status'])}\n'
                                    'تاريخ الاستحقاق: ${bill['due_date'] ?? 'غير محدد'}',
                                  ),
                                  isThreeLine: true,
                                ),
                              );
                            },
                          ),
                  ],
                ),
              ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => _showQuickActions(),
          icon: const Icon(Icons.add),
          label: const Text('إجراءات سريعة'),
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(title, textAlign: TextAlign.center),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'paid':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'overdue':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'paid':
        return 'مدفوعة';
      case 'pending':
        return 'معلقة';
      case 'overdue':
        return 'متأخرة';
      default:
        return 'غير محدد';
    }
  }

  void _navigateToScreen(Widget screen) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  void _navigateToBills() {
    _navigateToScreen(const BillsScreen());
  }

  void _navigateToSubscribers() {
    _navigateToScreen(const SubscribersScreen());
  }

  void _navigateToZones() {
    _navigateToScreen(const ZonesScreen());
  }

  void _navigateToReadings() {
    _navigateToScreen(const ReadingsScreen());
  }

  Future<void> _approveReading(int readingId) async {
    try {
      await _apiService.approveReading(readingId);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم الموافقة على القراءة بنجاح')),
      );
      _loadData(); // إعادة تحميل البيانات
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في الموافقة على القراءة: $e')),
      );
    }
  }

  Future<void> _rejectReading(int readingId) async {
    final reason = await _showRejectDialog();
    if (reason != null && reason.isNotEmpty) {
      try {
        await _apiService.rejectReading(readingId, reason);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم رفض القراءة')),
        );
        _loadData(); // إعادة تحميل البيانات
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في رفض القراءة: $e')),
        );
      }
    }
  }

  Future<String?> _showRejectDialog() async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => Directionality(
        textDirection: TextDirection.rtl,
        child: AlertDialog(
          title: const Text('رفض القراءة'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: 'سبب الرفض',
              hintText: 'أدخل سبب رفض القراءة',
            ),
            maxLines: 3,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, controller.text),
              child: const Text('رفض'),
            ),
          ],
        ),
      ),
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Directionality(
        textDirection: TextDirection.rtl,
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'إجراءات سريعة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.add_chart),
                title: const Text('تسجيل قراءة جديدة'),
                onTap: () {
                  Navigator.pop(context);
                  _navigateToReadings();
                },
              ),
              ListTile(
                leading: const Icon(Icons.receipt_long),
                title: const Text('إنشاء فواتير تلقائياً'),
                onTap: () {
                  Navigator.pop(context);
                  _generateBills();
                },
              ),
              ListTile(
                leading: const Icon(Icons.person_add),
                title: const Text('إضافة مشترك جديد'),
                onTap: () {
                  Navigator.pop(context);
                  _navigateToSubscribers();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _generateBills() async {
    final dueDate = await _showDatePicker();
    if (dueDate != null) {
      try {
        await _apiService.generateBills(dueDate);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء الفواتير بنجاح')),
        );
        _loadData();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في إنشاء الفواتير: $e')),
        );
      }
    }
  }

  Future<DateTime?> _showDatePicker() async {
    return showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now().add(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: 'اختر تاريخ الاستحقاق',
      cancelText: 'إلغاء',
      confirmText: 'موافق',
    );
  }
}