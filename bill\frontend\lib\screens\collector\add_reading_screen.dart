import 'package:flutter/material.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import '../../services/api_service.dart';

class AddReadingScreen extends StatefulWidget {
  final Map<String, dynamic>? subscriber;

  const AddReadingScreen({Key? key, this.subscriber}) : super(key: key);

  @override
  _AddReadingScreenState createState() => _AddReadingScreenState();
}

class _AddReadingScreenState extends State<AddReadingScreen> {
  final ApiService _apiService = ApiService();
  final ImagePicker _picker = ImagePicker();
  final _formKey = GlobalKey<FormState>();
  
  final _previousReadingController = TextEditingController();
  final _currentReadingController = TextEditingController();
  
  List<dynamic> _subscribers = [];
  Map<String, dynamic>? _selectedSubscriber;
  File? _selectedImage;
  bool _isLoading = false;
  bool _isProcessingOCR = false;

  @override
  void initState() {
    super.initState();
    _selectedSubscriber = widget.subscriber;
    if (widget.subscriber == null) {
      _loadSubscribers();
    }
  }

  Future<void> _loadSubscribers() async {
    try {
      final subscribers = await _apiService.getSubscribers();
      setState(() {
        _subscribers = subscribers ?? [];
      });
    } catch (e) {
      print('خطأ في تحميل المشتركين: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إضافة قراءة جديدة'),
        actions: [
          if (_selectedImage != null)
            IconButton(
              icon: Icon(Icons.auto_awesome),
              onPressed: _isProcessingOCR ? null : _processImageWithOCR,
              tooltip: 'استخراج تلقائي',
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Subscriber selection
              if (widget.subscriber == null) ...[
                Text(
                  'اختيار المشترك',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                SizedBox(height: 8),
                DropdownButtonFormField<Map<String, dynamic>>(
                  value: _selectedSubscriber,
                  decoration: InputDecoration(
                    labelText: 'المشترك',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                  items: _subscribers.map((subscriber) {
                    return DropdownMenuItem<Map<String, dynamic>>(
                      value: subscriber,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(subscriber['name'] ?? ''),
                          Text(
                            'رقم الاشتراك: ${subscriber['subscription_no'] ?? ''}',
                            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedSubscriber = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'ير��ى اختيار المشترك';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 24),
              ] else ...[
                // Show selected subscriber info
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'معلومات المشترك',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        SizedBox(height: 8),
                        _buildInfoRow('الاسم', _selectedSubscriber!['name'] ?? ''),
                        _buildInfoRow('رقم الاشتراك', _selectedSubscriber!['subscription_no'] ?? ''),
                        _buildInfoRow('العنوان', _selectedSubscriber!['address'] ?? ''),
                        if (_selectedSubscriber!['zone'] != null)
                          _buildInfoRow('المنطقة', _selectedSubscriber!['zone']['name'] ?? ''),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 24),
              ],

              // Reading inputs
              Text(
                'بيانات القراءة',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _previousReadingController,
                      decoration: InputDecoration(
                        labelText: 'القراءة السابقة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.history),
                        suffixText: 'كيلوواط',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'مطلوب';
                        }
                        if (double.tryParse(value) == null) {
                          return 'رقم غير صحيح';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _currentReadingController,
                      decoration: InputDecoration(
                        labelText: 'القراءة الحالية',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.flash_on),
                        suffixText: 'كيلوواط',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'مطلوب';
                        }
                        final current = double.tryParse(value);
                        final previous = double.tryParse(_previousReadingController.text);
                        
                        if (current == null) {
                          return 'رقم غير صحيح';
                        }
                        if (previous != null && current < previous) {
                          return 'يجب أن تكون أكبر من السابقة';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 16),
              
              // Consumption display
              if (_previousReadingController.text.isNotEmpty && _currentReadingController.text.isNotEmpty)
                _buildConsumptionCard(),
              
              SizedBox(height: 24),
              
              // Image capture section
              Text(
                'صورة العداد',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 16),
              
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImageFromCamera,
                      icon: Icon(Icons.camera_alt),
                      label: Text('التقاط صورة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImageFromGallery,
                      icon: Icon(Icons.photo_library),
                      label: Text('من المعرض'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: 16),
              
              // Image preview
              if (_selectedImage != null) ...[
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(8),
                    child: Column(
                      children: [
                        Container(
                          height: 200,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              _selectedImage!,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: _isProcessingOCR ? null : _processImageWithOCR,
                                icon: _isProcessingOCR 
                                    ? SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(strokeWidth: 2),
                                      )
                                    : Icon(Icons.auto_awesome),
                                label: Text(_isProcessingOCR ? 'جاري المعالجة...' : 'استخراج تلقائي'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.purple,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            ElevatedButton.icon(
                              onPressed: () {
                                setState(() {
                                  _selectedImage = null;
                                });
                              },
                              icon: Icon(Icons.delete),
                              label: Text('حذف'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
              
              SizedBox(height: 32),
              
              // Submit button
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _submitReading,
                  icon: _isLoading 
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Icon(Icons.save),
                  label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ القراءة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    textStyle: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildConsumptionCard() {
    final previous = double.tryParse(_previousReadingController.text) ?? 0;
    final current = double.tryParse(_currentReadingController.text) ?? 0;
    final consumption = current - previous;
    
    return Card(
      color: consumption >= 0 ? Colors.green[50] : Colors.red[50],
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.flash_on,
              color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
            ),
            SizedBox(width: 8),
            Text(
              'الاستهلاك: ${consumption.toStringAsFixed(2)} كيلوواط',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  Future<void> _pickImageFromGallery() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  Future<void> _processImageWithOCR() async {
    if (_selectedImage == null) return;

    setState(() {
      _isProcessingOCR = true;
    });

    try {
      final result = await _apiService.uploadImageForOCR(_selectedImage!);
      if (result != null && result['success'] == true) {
        setState(() {
          if (result['current_reading'] != null) {
            _currentReadingController.text = result['current_reading'].toString();
          }
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم استخراج القراءة تلقائياً'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في استخراج القراءة من الصورة'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في معالجة الصورة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isProcessingOCR = false;
      });
    }
  }

  Future<void> _submitReading() async {
    if (!_formKey.currentState!.validate()) return;
    
    if (_selectedSubscriber == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('يرجى اختيار المشترك')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _apiService.createReading({
        'subscriber_id': _selectedSubscriber!['id'],
        'previous_reading': double.parse(_previousReadingController.text),
        'current_reading': double.parse(_currentReadingController.text),
      }, _selectedImage);

      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حفظ القراءة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حفظ القراءة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حفظ القراءة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _previousReadingController.dispose();
    _currentReadingController.dispose();
    super.dispose();
  }
}