import 'package:flutter/material.dart';
import '../../services/auth_service.dart';
import '../../services/api_service.dart';
import '../auth/login_screen.dart';
import 'subscribers_list_screen.dart';
import 'add_reading_screen.dart';
import 'my_readings_screen.dart';

class CollectorDashboardScreen extends StatefulWidget {
  @override
  _CollectorDashboardScreenState createState() => _CollectorDashboardScreenState();
}

class _CollectorDashboardScreenState extends State<CollectorDashboardScreen> {
  final AuthService _authService = AuthService();
  final ApiService _apiService = ApiService();
  
  Map<String, dynamic>? _stats;
  Map<String, dynamic>? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = await _authService.getCurrentUser();
      final subscribers = await _apiService.getSubscribers();
      final readings = await _apiService.getReadings();

      // Filter data for current collector
      final myReadings = readings?.where((r) => r['collector_id'] == user?['id']).toList() ?? [];
      final pendingReadings = myReadings.where((r) => r['status'] == 'pending').toList();
      final approvedReadings = myReadings.where((r) => r['status'] == 'approved').toList();
      final rejectedReadings = myReadings.where((r) => r['status'] == 'rejected').toList();

      setState(() {
        _currentUser = user;
        _stats = {
          'subscribers_count': subscribers?.length ?? 0,
          'total_readings': myReadings.length,
          'pending_readings': pendingReadings.length,
          'approved_readings': approvedReadings.length,
          'rejected_readings': rejectedReadings.length,
          'approval_rate': myReadings.isNotEmpty 
              ? (approvedReadings.length / myReadings.length * 100)
              : 0.0,
        };
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _logout() async {
    final success = await _authService.logout();
    if (success) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => LoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('لوحة تحكم المحصل'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'profile':
                  _showProfile();
                  break;
                case 'logout':
                  _logout();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'profile',
                child: Row(
                  children: [
                    Icon(Icons.person, size: 20),
                    SizedBox(width: 8),
                    Text('الملف الشخصي'),
                  ],
                ),
              ),
              PopupMenuDivider(),
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('تسجيل الخروج', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome message
                    _buildWelcomeCard(),
                    SizedBox(height: 24),
                    
                    // Statistics cards
                    _buildStatsCards(),
                    SizedBox(height: 24),
                    
                    // Quick actions
                    _buildQuickActions(),
                    SizedBox(height: 24),
                    
                    // Performance summary
                    _buildPerformanceSummary(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(20),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.blue[100],
              child: Icon(
                Icons.person_search,
                size: 32,
                color: Colors.blue[800],
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً، ${_currentUser?['username'] ?? ''}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'محصل قراءات العدادات',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'اليوم: ${_formatDate(DateTime.now())}',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    if (_stats == null) return SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائياتي',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي القراءات',
              _stats!['total_readings'].toString(),
              Icons.assessment,
              Colors.blue,
            ),
            _buildStatCard(
              'قراءات معلقة',
              _stats!['pending_readings'].toString(),
              Icons.pending_actions,
              Colors.orange,
            ),
            _buildStatCard(
              'قراءات معتمدة',
              _stats!['approved_readings'].toString(),
              Icons.check_circle,
              Colors.green,
            ),
            _buildStatCard(
              'قراءات مرفوضة',
              _stats!['rejected_readings'].toString(),
              Icons.cancel,
              Colors.red,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 1,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 4,
          children: [
            _buildActionCard(
              'عرض المشتركين',
              'قائمة المشتركين في منطقتي',
              Icons.people,
              Colors.blue,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => SubscribersListScreen())),
            ),
            _buildActionCard(
              'إضافة قراءة جديدة',
              'التقاط صورة وإدخال قراءة العداد',
              Icons.add_a_photo,
              Colors.green,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => AddReadingScreen())),
            ),
            _buildActionCard(
              'قراءاتي',
              'عرض جميع القراءات التي قمت بإدخالها',
              Icons.history,
              Colors.orange,
              () => Navigator.push(context, MaterialPageRoute(builder: (context) => MyReadingsScreen())),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceSummary() {
    if (_stats == null) return SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص الأداء',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        SizedBox(height: 16),
        Card(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                _buildPerformanceRow(
                  'معدل الاعتماد',
                  '${_stats!['approval_rate'].toStringAsFixed(1)}%',
                  Icons.trending_up,
                  _getPerformanceColor(_stats!['approval_rate']),
                ),
                Divider(),
                _buildPerformanceRow(
                  'القراءات هذا الشهر',
                  _stats!['total_readings'].toString(),
                  Icons.calendar_month,
                  Colors.blue,
                ),
                Divider(),
                _buildPerformanceRow(
                  'متوسط القراءات اليومية',
                  '${(_stats!['total_readings'] / 30).toStringAsFixed(1)}',
                  Icons.today,
                  Colors.green,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceRow(String title, String value, IconData icon, Color color) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: color),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Color _getPerformanceColor(double rate) {
    if (rate >= 90) return Colors.green;
    if (rate >= 70) return Colors.orange;
    return Colors.red;
  }

  String _formatDate(DateTime date) {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _showProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('الملف الشخصي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileRow('اسم المستخدم', _currentUser?['username'] ?? ''),
            _buildProfileRow('الدور', 'محصل'),
            _buildProfileRow('تاريخ الانضمام', _formatDate(DateTime.now())),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}