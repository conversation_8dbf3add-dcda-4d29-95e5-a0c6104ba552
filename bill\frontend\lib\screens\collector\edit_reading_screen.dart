import 'package:flutter/material.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import '../../services/api_service.dart';

class EditReadingScreen extends StatefulWidget {
  final Map<String, dynamic> reading;

  const EditReadingScreen({Key? key, required this.reading}) : super(key: key);

  @override
  _EditReadingScreenState createState() => _EditReadingScreenState();
}

class _EditReadingScreenState extends State<EditReadingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _previousReadingController = TextEditingController();
  final _currentReadingController = TextEditingController();
  final ApiService _apiService = ApiService();
  final ImagePicker _picker = ImagePicker();

  File? _selectedImage;
  bool _isLoading = false;
  bool _isProcessingOCR = false;
  String _billStatus = 'not_paid';

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    _previousReadingController.text = widget.reading['previous_reading'].toString();
    _currentReadingController.text = widget.reading['current_reading'].toString();
    _billStatus = widget.reading['bill_status'] ?? 'not_paid';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تعديل القراءة'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Subscriber info
              Card(
                color: Colors.blue[50],
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معلومات المشترك',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.blue[700],
                        ),
                      ),
                      SizedBox(height: 12),
                      _buildInfoRow('الاسم', widget.reading['subscriber']?['name'] ?? ''),
                      _buildInfoRow('رقم الاشتراك', widget.reading['subscriber']?['subscription_no'] ?? ''),
                      _buildInfoRow('العنوان', widget.reading['subscriber']?['address'] ?? ''),
                      _buildInfoRow('المنطقة', widget.reading['subscriber']?['zone']?['name'] ?? ''),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 16),

              // Rejection reason
              if (widget.reading['review_note'] != null) ...[
                Card(
                  color: Colors.red[50],
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.error_outline, color: Colors.red[700]),
                            SizedBox(width: 8),
                            Text(
                              'سبب الرفض:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.red[700],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          widget.reading['review_note'],
                          style: TextStyle(color: Colors.red[600]),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16),
              ],

              // Reading inputs
              Text(
                'القراءات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _previousReadingController,
                      decoration: InputDecoration(
                        labelText: 'القراءة السابقة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.history),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال القراءة السابقة';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        return null;
                      },
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _currentReadingController,
                      decoration: InputDecoration(
                        labelText: 'القراءة الحالية',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.electric_meter),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال القراءة الحالية';
                        }
                        final current = double.tryParse(value);
                        final previous = double.tryParse(_previousReadingController.text);
                        if (current == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        if (previous != null && current < previous) {
                          return 'القراءة الحالية يجب أن تكون أكبر من السابقة';
                        }
                        return null;
                      },
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 16),

              // Consumption card
              _buildConsumptionCard(),

              SizedBox(height: 16),

              // Image section
              Text(
                'صورة العداد (اختيارية)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              SizedBox(height: 8),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImageFromCamera,
                      icon: Icon(Icons.camera_alt),
                      label: Text('التقاط صورة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickImageFromGallery,
                      icon: Icon(Icons.photo_library),
                      label: Text('من المعرض'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

              if (_selectedImage != null) ...[
                SizedBox(height: 16),
                Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _selectedImage!,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isProcessingOCR ? null : _processImageWithOCR,
                        icon: _isProcessingOCR
                            ? SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : Icon(Icons.auto_awesome),
                        label: Text(_isProcessingOCR ? 'جاري المعالجة...' : 'استخراج تلقائي'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          _selectedImage = null;
                        });
                      },
                      icon: Icon(Icons.delete),
                      label: Text('حذف'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],

              SizedBox(height: 16),

              // Payment status selection
              Card(
                color: Colors.amber[50],
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'حالة الدفع:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.amber[800],
                        ),
                      ),
                      SizedBox(height: 12),
                      Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: RadioListTile<String>(
                                  title: Text('مدفوعة'),
                                  value: 'paid',
                                  groupValue: _billStatus,
                                  onChanged: (value) {
                                    setState(() {
                                      _billStatus = value!;
                                    });
                                  },
                                  activeColor: Colors.green,
                                ),
                              ),
                              Expanded(
                                child: RadioListTile<String>(
                                  title: Text('غير مدفوعة'),
                                  value: 'not_paid',
                                  groupValue: _billStatus,
                                  onChanged: (value) {
                                    setState(() {
                                      _billStatus = value!;
                                    });
                                  },
                                  activeColor: Colors.red,
                                ),
                              ),
                            ],
                          ),
                          RadioListTile<String>(
                            title: Text('متأخرة'),
                            value: 'late',
                            groupValue: _billStatus,
                            onChanged: (value) {
                              setState(() {
                                _billStatus = value!;
                              });
                            },
                            activeColor: Colors.orange,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: 24),

              // Submit button
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _updateReading,
                  icon: _isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Icon(Icons.save),
                  label: Text(_isLoading ? 'جاري التحديث...' : 'تحديث القراءة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    textStyle: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Widget _buildConsumptionCard() {
    final previous = double.tryParse(_previousReadingController.text) ?? 0;
    final current = double.tryParse(_currentReadingController.text) ?? 0;
    final consumption = current - previous;
    final estimatedCost = consumption * 257;

    return Card(
      color: consumption >= 0 ? Colors.green[50] : Colors.red[50],
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.flash_on,
                  color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                ),
                SizedBox(width: 8),
                Text(
                  'الاستهلاك: ${consumption.toStringAsFixed(2)} كيلوواط',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ],
            ),
            if (consumption > 0) ...[
              SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.attach_money,
                    color: Colors.blue[700],
                  ),
                  SizedBox(width: 8),
                  Text(
                    'التكلفة المتوقعة: ${estimatedCost.toStringAsFixed(0)} ريال يمني',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  Future<void> _pickImageFromGallery() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _selectedImage = File(image.path);
      });
    }
  }

  Future<void> _processImageWithOCR() async {
    if (_selectedImage == null) return;

    setState(() {
      _isProcessingOCR = true;
    });

    try {
      final result = await _apiService.uploadImageForOCR(_selectedImage!);
      if (result != null && result['success'] == true) {
        setState(() {
          if (result['current_reading'] != null) {
            _currentReadingController.text = result['current_reading'].toString();
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم استخراج القراءة تلقائياً'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في استخراج القراءة من الصورة'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في معالجة الصورة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isProcessingOCR = false;
      });
    }
  }

  Future<void> _updateReading() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _apiService.updateReading(widget.reading['id'], {
        'previous_reading': double.parse(_previousReadingController.text),
        'current_reading': double.parse(_currentReadingController.text),
        'bill_status': _billStatus,
      }, _selectedImage);

      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث القراءة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث القراءة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث القراءة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _previousReadingController.dispose();
    _currentReadingController.dispose();
    super.dispose();
  }
}
