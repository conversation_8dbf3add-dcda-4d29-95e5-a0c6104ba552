import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class MyReadingsScreen extends StatefulWidget {
  @override
  _MyReadingsScreenState createState() => _MyReadingsScreenState();
}

class _MyReadingsScreenState extends State<MyReadingsScreen> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  late TabController _tabController;
  
  List<dynamic> _allReadings = [];
  List<dynamic> _pendingReadings = [];
  List<dynamic> _approvedReadings = [];
  List<dynamic> _rejectedReadings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadReadings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReadings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final readings = await _apiService.getReadings();
      
      if (readings != null) {
        setState(() {
          _allReadings = readings;
          _pendingReadings = readings.where((r) => r['status'] == 'pending').toList();
          _approvedReadings = readings.where((r) => r['status'] == 'approved').toList();
          _rejectedReadings = readings.where((r) => r['status'] == 'rejected').toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('خطأ في تحميل القراءات: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('قراءاتي'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadReadings,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              text: 'الكل',
              icon: Badge(
                label: Text(_allReadings.length.toString()),
                child: Icon(Icons.list),
              ),
            ),
            Tab(
              text: 'معلقة',
              icon: Badge(
                label: Text(_pendingReadings.length.toString()),
                child: Icon(Icons.pending_actions),
              ),
            ),
            Tab(
              text: 'معتمدة',
              icon: Badge(
                label: Text(_approvedReadings.length.toString()),
                child: Icon(Icons.check_circle),
              ),
            ),
            Tab(
              text: 'مرفوضة',
              icon: Badge(
                label: Text(_rejectedReadings.length.toString()),
                child: Icon(Icons.cancel),
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildReadingsList(_allReadings, 'all'),
                _buildReadingsList(_pendingReadings, 'pending'),
                _buildReadingsList(_approvedReadings, 'approved'),
                _buildReadingsList(_rejectedReadings, 'rejected'),
              ],
            ),
    );
  }

  Widget _buildReadingsList(List<dynamic> readings, String type) {
    if (readings.isEmpty) {
      return _buildEmptyState(type);
    }

    return RefreshIndicator(
      onRefresh: _loadReadings,
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: readings.length,
        itemBuilder: (context, index) {
          final reading = readings[index];
          return _buildReadingCard(reading);
        },
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    String message;
    IconData icon;
    
    switch (type) {
      case 'pending':
        message = 'لا توجد قراءات معلقة';
        icon = Icons.pending_actions;
        break;
      case 'approved':
        message = 'لا توجد قراءات معتمدة';
        icon = Icons.check_circle;
        break;
      case 'rejected':
        message = 'لا توجد قراءات مرفوضة';
        icon = Icons.cancel;
        break;
      default:
        message = 'لا توجد قراءات';
        icon = Icons.assessment;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingCard(Map<String, dynamic> reading) {
    Color statusColor;
    String statusText;
    IconData statusIcon;
    
    switch (reading['status']) {
      case 'pending':
        statusColor = Colors.orange;
        statusText = 'معلقة';
        statusIcon = Icons.pending_actions;
        break;
      case 'approved':
        statusColor = Colors.green;
        statusText = 'معتمدة';
        statusIcon = Icons.check_circle;
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusText = 'مرفوضة';
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'غير معروف';
        statusIcon = Icons.help;
    }

    final consumption = reading['current_reading'] - reading['previous_reading'];

    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 3,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with subscriber name and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reading['subscriber']?['name'] ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'رقم الاشتراك: ${reading['subscriber']?['subscription_no'] ?? ''}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: statusColor),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Reading values
            Row(
              children: [
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة السابقة',
                    reading['previous_reading'].toString(),
                    Colors.grey[600]!,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة الحالية',
                    reading['current_reading'].toString(),
                    Colors.blue[700]!,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            // Consumption
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: consumption >= 0 ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: consumption >= 0 ? Colors.green[200]! : Colors.red[200]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.flash_on,
                    color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                  ),
                  SizedBox(width: 8),
                  Text(
                    'الاستهلاك: ${consumption.toStringAsFixed(2)} كيلوواط',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 12),
            
            // Date and reviewer info
            _buildInfoRow('تاريخ الإدخال', _formatDate(reading['created_at'])),
            if (reading['reviewer'] != null)
              _buildInfoRow('المراجع', reading['reviewer']['username']),
            
            // Review note for rejected readings
            if (reading['review_note'] != null) ...[
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.note, size: 16, color: Colors.red[700]),
                        SizedBox(width: 8),
                        Text(
                          'ملاحظة المراجع:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.red[700],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      reading['review_note'],
                      style: TextStyle(
                        color: Colors.red[800],
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Image preview if available
            if (reading['image_path'] != null) ...[
              SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.image, size: 16, color: Colors.grey[600]),
                  SizedBox(width: 4),
                  Text(
                    'صورة العداد متاحة',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  Spacer(),
                  TextButton(
                    onPressed: () => _showImageDialog(reading['image_path']),
                    child: Text('عرض الصورة'),
                  ),
                ],
              ),
            ],
            
            // Action buttons for pending readings
            if (reading['status'] == 'pending') ...[
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _editReading(reading),
                      icon: Icon(Icons.edit, size: 16),
                      label: Text('تعديل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _showReadingDetails(reading),
                    icon: Icon(Icons.info, size: 16),
                    label: Text('التفاصيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ] else ...[
              SizedBox(height: 16),
              Center(
                child: ElevatedButton.icon(
                  onPressed: () => _showReadingDetails(reading),
                  icon: Icon(Icons.info, size: 16),
                  label: Text('عرض التفاصيل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildReadingInfo(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  void _showImageDialog(String imagePath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppBar(
              title: Text('صورة العداد'),
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            Container(
              height: 400,
              width: double.infinity,
              child: Image.network(
                'http://localhost:8000/storage/$imagePath',
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.broken_image, size: 48, color: Colors.grey[400]),
                          Text('لا يمكن تحميل الصورة'),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _editReading(Map<String, dynamic> reading) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('ميزة تعديل القراءة ستكون متاحة قريباً')),
    );
  }

  void _showReadingDetails(Map<String, dynamic> reading) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل القراءة'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('رقم القراءة', reading['id'].toString()),
              _buildDetailRow('اسم المشترك', reading['subscriber']?['name'] ?? ''),
              _buildDetailRow('رقم الاشتراك', reading['subscriber']?['subscription_no'] ?? ''),
              _buildDetailRow('القراءة السابقة', reading['previous_reading'].toString()),
              _buildDetailRow('القراءة الحالية', reading['current_reading'].toString()),
              _buildDetailRow('الاستهلاك', '${(reading['current_reading'] - reading['previous_reading']).toStringAsFixed(2)} كيلوواط'),
              _buildDetailRow('الحالة', _getStatusText(reading['status'])),
              _buildDetailRow('تاريخ الإدخال', _formatDate(reading['created_at'])),
              if (reading['reviewer'] != null)
                _buildDetailRow('المراجع', reading['reviewer']['username']),
              if (reading['review_note'] != null)
                _buildDetailRow('ملاحظة المراجع', reading['review_note']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'approved':
        return 'معتمدة';
      case 'rejected':
        return 'مرفوضة';
      default:
        return status;
    }
  }
}