import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class RejectedReadingsScreen extends StatefulWidget {
  @override
  _RejectedReadingsScreenState createState() => _RejectedReadingsScreenState();
}

class _RejectedReadingsScreenState extends State<RejectedReadingsScreen> {
  final ApiService _apiService = ApiService();
  List<Map<String, dynamic>> _rejectedReadings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRejectedReadings();
  }

  Future<void> _loadRejectedReadings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final readings = await _apiService.getReadings();
      if (readings != null) {
        setState(() {
          _rejectedReadings = (readings as List)
              .where((reading) => reading['status'] == 'rejected')
              .cast<Map<String, dynamic>>()
              .toList();
        });
      }
    } catch (e) {
      print('خطأ في تحميل القراءات المرفوضة: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('القراءات المرفوضة'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadRejectedReadings,
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _rejectedReadings.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 64,
                        color: Colors.green[400],
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد قراءات مرفوضة',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'جميع قراءاتك تم قبولها',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadRejectedReadings,
                  child: ListView.builder(
                    padding: EdgeInsets.all(16),
                    itemCount: _rejectedReadings.length,
                    itemBuilder: (context, index) {
                      final reading = _rejectedReadings[index];
                      return _buildRejectedReadingCard(reading);
                    },
                  ),
                ),
    );
  }

  Widget _buildRejectedReadingCard(Map<String, dynamic> reading) {
    final consumption = _calculateConsumption(reading);
    final estimatedCost = consumption * 257;

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with subscriber info
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue[700]),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    reading['subscriber']?['name'] ?? 'غير محدد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'مرفوضة',
                    style: TextStyle(
                      color: Colors.red[700],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Reading details
            Row(
              children: [
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة السابقة',
                    reading['previous_reading'].toString(),
                    Colors.grey[600]!,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة الحالية',
                    reading['current_reading'].toString(),
                    Colors.blue[600]!,
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Consumption and cost info
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.flash_on, color: Colors.orange[700]),
                      SizedBox(width: 8),
                      Text(
                        'الاستهلاك: ${consumption.toStringAsFixed(2)} كيلوواط',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.orange[700],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.attach_money, color: Colors.green[700]),
                      SizedBox(width: 8),
                      Text(
                        'التكلفة: ${_formatAmount(estimatedCost)} ريال يمني',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        _getBillStatusIcon(reading['bill_status']),
                        color: _getBillStatusColor(reading['bill_status']),
                      ),
                      SizedBox(width: 8),
                      Text(
                        'حالة الدفع: ${_getBillStatusLabel(reading['bill_status'])}',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: _getBillStatusColor(reading['bill_status']),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            SizedBox(height: 16),

            // Rejection reason
            if (reading['review_note'] != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red[700]),
                        SizedBox(width: 8),
                        Text(
                          'سبب الرفض:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red[700],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      reading['review_note'],
                      style: TextStyle(color: Colors.red[600]),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
            ],

            // Action button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _editReading(reading),
                icon: Icon(Icons.edit),
                label: Text('تعديل القراءة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[700],
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingInfo(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _editReading(Map<String, dynamic> reading) {
    Navigator.pushNamed(
      context,
      '/edit-reading',
      arguments: reading,
    ).then((result) {
      if (result == true) {
        _loadRejectedReadings();
      }
    });
  }

  String _getBillStatusLabel(String? status) {
    switch (status) {
      case 'paid':
        return 'مدفوعة';
      case 'not_paid':
        return 'غير مدفوعة';
      case 'late':
        return 'متأخرة';
      default:
        return 'غير محدد';
    }
  }

  IconData _getBillStatusIcon(String? status) {
    switch (status) {
      case 'paid':
        return Icons.check_circle;
      case 'not_paid':
        return Icons.pending;
      case 'late':
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  Color _getBillStatusColor(String? status) {
    switch (status) {
      case 'paid':
        return Colors.green[700]!;
      case 'not_paid':
        return Colors.red[700]!;
      case 'late':
        return Colors.orange[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  String _formatAmount(dynamic amount) {
    if (amount == null) return '0';

    // إذا كان المبلغ string، حاول تحويله إلى double
    if (amount is String) {
      final parsed = double.tryParse(amount);
      if (parsed != null) {
        return parsed.toStringAsFixed(0);
      }
      return amount; // إرجاع القيمة كما هي إذا فشل التحويل
    }

    // إذا كان المبلغ double أو int
    if (amount is num) {
      return amount.toStringAsFixed(0);
    }

    return amount.toString();
  }

  double _calculateConsumption(Map<String, dynamic> reading) {
    final currentReading = double.tryParse(reading['current_reading'].toString()) ?? 0.0;
    final previousReading = double.tryParse(reading['previous_reading'].toString()) ?? 0.0;
    return currentReading - previousReading;
  }
}
