import 'package:flutter/material.dart';
import '../../services/api_service.dart';
import 'add_reading_screen.dart';

class SubscribersListScreen extends StatefulWidget {
  @override
  _SubscribersListScreenState createState() => _SubscribersListScreenState();
}

class _SubscribersListScreenState extends State<SubscribersListScreen> {
  final ApiService _apiService = ApiService();
  List<dynamic> _subscribers = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadSubscribers();
  }

  Future<void> _loadSubscribers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final subscribers = await _apiService.getSubscribers();
      setState(() {
        _subscribers = subscribers ?? [];
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل المشتركين: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<dynamic> get _filteredSubscribers {
    if (_searchQuery.isEmpty) return _subscribers;
    
    return _subscribers.where((subscriber) {
      final name = subscriber['name']?.toString().toLowerCase() ?? '';
      final subscriptionNo = subscriber['subscription_no']?.toString().toLowerCase() ?? '';
      final address = subscriber['address']?.toString().toLowerCase() ?? '';
      final query = _searchQuery.toLowerCase();
      
      return name.contains(query) || 
             subscriptionNo.contains(query) || 
             address.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('المشتركين في منطقتي'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadSubscribers,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: EdgeInsets.all(16),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'البحث في المشتركين...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          
          // Subscribers list
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    onRefresh: _loadSubscribers,
                    child: _filteredSubscribers.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            itemCount: _filteredSubscribers.length,
                            itemBuilder: (context, index) {
                              final subscriber = _filteredSubscribers[index];
                              return _buildSubscriberCard(subscriber);
                            },
                          ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _searchQuery.isEmpty ? Icons.people_outline : Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty ? 'لا يوجد مشتركين' : 'لا توجد نتائج للبحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            _searchQuery.isEmpty 
                ? 'لا توجد مشتركين في منطقتك حالياً'
                : 'جرب البحث بكلمات مختلفة',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriberCard(Map<String, dynamic> subscriber) {
    final hasLatestReading = subscriber['latest_reading'] != null;
    final latestReading = subscriber['latest_reading'];

    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with name and subscription number
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.blue[100],
                  child: Icon(Icons.person, color: Colors.blue[800]),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        subscriber['name'] ?? '',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'رقم الاشتراك: ${subscriber['subscription_no'] ?? ''}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _navigateToAddReading(subscriber),
                  icon: Icon(Icons.add_a_photo, size: 16),
                  label: Text('قراءة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            // Address
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    subscriber['address'] ?? '',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 13,
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8),
            
            // Zone info
            if (subscriber['zone'] != null) ...[
              Row(
                children: [
                  Icon(Icons.map, size: 16, color: Colors.grey[600]),
                  SizedBox(width: 4),
                  Text(
                    'المنطقة: ${subscriber['zone']['name']}',
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 13,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8),
            ],
            
            // Latest reading info
            if (hasLatestReading) ...[
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'آخر قراءة:',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.green[800],
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'القراءة: ${latestReading['current_reading']}',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'التاريخ: ${_formatDate(latestReading['created_at'])}',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                    if (latestReading['status'] != null) ...[
                      SizedBox(height: 4),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getStatusColor(latestReading['status']).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: _getStatusColor(latestReading['status'])),
                        ),
                        child: Text(
                          _getStatusText(latestReading['status']),
                          style: TextStyle(
                            color: _getStatusColor(latestReading['status']),
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ] else ...[
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, size: 16, color: Colors.orange[700]),
                    SizedBox(width: 8),
                    Text(
                      'لا توجد قراءات سابقة',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'معلقة';
      case 'approved':
        return 'معتمدة';
      case 'rejected':
        return 'مرفوضة';
      default:
        return status;
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  void _navigateToAddReading(Map<String, dynamic> subscriber) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddReadingScreen(subscriber: subscriber),
      ),
    ).then((_) {
      // Refresh the list when returning from add reading screen
      _loadSubscribers();
    });
  }
}