import 'package:flutter/material.dart';
import 'dart:convert';
import '../services/connection_test_service.dart';

class ConnectionTestScreen extends StatefulWidget {
  @override
  _ConnectionTestScreenState createState() => _ConnectionTestScreenState();
}

class _ConnectionTestScreenState extends State<ConnectionTestScreen> {
  final ConnectionTestService _testService = ConnectionTestService();
  
  Map<String, dynamic>? _testResults;
  bool _isRunning = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('اختبار الاتصال'),
        backgroundColor: Colors.blue[800],
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header card
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.network_check,
                      size: 48,
                      color: Colors.blue[800],
                    ),
                    SizedBox(height: 8),
                    Text(
                      'اختبار اتصال التطبيق بالخادم',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'يتم اختبار الاتصال مع خادم Laravel وخدمة OCR',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Test button
            ElevatedButton.icon(
              onPressed: _isRunning ? null : _runTests,
              icon: _isRunning 
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(Icons.play_arrow),
              label: Text(_isRunning ? 'جاري الاختبار...' : 'بدء الاختبار'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.blue[700],
                foregroundColor: Colors.white,
              ),
            ),
            
            SizedBox(height: 24),
            
            // Results
            Expanded(
              child: _testResults == null 
                  ? _buildWaitingState()
                  : _buildResults(),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildWaitingState() {
    return Card(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.touch_app,
              size: 64,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16),
            Text(
              'اضغط على "بدء الاختبار" لفحص الاتصال',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildResults() {
    final summary = _testResults!['summary'];
    final tests = _testResults!['tests'];
    
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Summary card
          Card(
            color: _getSummaryColor(summary['overall_status']),
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(
                    _getSummaryIcon(summary['overall_status']),
                    size: 48,
                    color: Colors.white,
                  ),
                  SizedBox(height: 8),
                  Text(
                    _getSummaryText(summary['overall_status']),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '${summary['successful_tests']}/${summary['total_tests']} اختبارات نجحت',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          SizedBox(height: 16),
          
          // Individual test results
          _buildTestResult('اتصال Laravel', tests['laravel_connection']),
          _buildTestResult('خدمة OCR', tests['ocr_connection']),
          _buildTestResult('تسجيل الدخول', tests['login']),
          
          if (tests['authenticated_request'] != null)
            _buildTestResult('الطلبات المصادق عليها', tests['authenticated_request']),
          
          if (tests['api_endpoints'] != null)
            _buildApiEndpointsResults(tests['api_endpoints']),
          
          SizedBox(height: 16),
          
          // Recommendations
          _buildRecommendations(),
          
          SizedBox(height: 16),
          
          // Raw results (expandable)
          _buildRawResults(),
        ],
      ),
    );
  }
  
  Widget _buildTestResult(String title, Map<String, dynamic>? result) {
    if (result == null) return SizedBox();
    
    return Card(
      child: ListTile(
        leading: Icon(
          result['success'] ? Icons.check_circle : Icons.error,
          color: result['success'] ? Colors.green : Colors.red,
        ),
        title: Text(title),
        subtitle: Text(result['message'] ?? 'لا توجد رسالة'),
        trailing: Text(
          'كود: ${result['status_code']}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ),
    );
  }
  
  Widget _buildApiEndpointsResults(Map<String, dynamic> endpoints) {
    return Card(
      child: ExpansionTile(
        leading: Icon(Icons.api),
        title: Text('نقاط النهاية API'),
        children: endpoints.entries.map((entry) {
          final result = entry.value;
          return ListTile(
            dense: true,
            leading: Icon(
              result['success'] ? Icons.check : Icons.close,
              color: result['success'] ? Colors.green : Colors.red,
              size: 20,
            ),
            title: Text(
              entry.key,
              style: TextStyle(fontSize: 14),
            ),
            trailing: Text(
              '${result['status_code']}',
              style: TextStyle(fontSize: 12),
            ),
          );
        }).toList(),
      ),
    );
  }
  
  Widget _buildRecommendations() {
    final recommendations = _testService.getConnectionRecommendations(_testResults!);
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'التوصيات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            ...recommendations.map((rec) => Padding(
              padding: EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                  Expanded(child: Text(rec)),
                ],
              ),
            )).toList(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRawResults() {
    return Card(
      child: ExpansionTile(
        leading: Icon(Icons.code),
        title: Text('النتائج الخام'),
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                JsonEncoder.withIndent('  ').convert(_testResults),
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Future<void> _runTests() async {
    setState(() {
      _isRunning = true;
      _testResults = null;
    });
    
    try {
      final results = await _testService.runFullConnectionTest();
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      setState(() {
        _testResults = {
          'error': e.toString(),
          'summary': {
            'overall_status': 'FAIL',
            'total_tests': 0,
            'successful_tests': 0,
          },
          'tests': {},
        };
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }
  
  Color _getSummaryColor(String status) {
    switch (status) {
      case 'ALL_PASS': return Colors.green;
      case 'PARTIAL_PASS': return Colors.orange;
      default: return Colors.red;
    }
  }
  
  IconData _getSummaryIcon(String status) {
    switch (status) {
      case 'ALL_PASS': return Icons.check_circle;
      case 'PARTIAL_PASS': return Icons.warning;
      default: return Icons.error;
    }
  }
  
  String _getSummaryText(String status) {
    switch (status) {
      case 'ALL_PASS': return 'جميع الاختبارات نجحت';
      case 'PARTIAL_PASS': return 'بعض الاختبارات نجحت';
      default: return 'فشلت الاختبارات';
    }
  }
}
