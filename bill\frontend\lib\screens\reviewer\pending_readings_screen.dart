import 'package:flutter/material.dart';
import '../../services/api_service.dart';

class PendingReadingsScreen extends StatefulWidget {
  @override
  _PendingReadingsScreenState createState() => _PendingReadingsScreenState();
}

class _PendingReadingsScreenState extends State<PendingReadingsScreen> {
  final ApiService _apiService = ApiService();
  List<dynamic> _pendingReadings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPendingReadings();
  }

  Future<void> _loadPendingReadings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final pendingReadings = await _apiService.getPendingReadings();
      setState(() {
        _pendingReadings = pendingReadings ?? [];
        _isLoading = false;
      });
    } catch (e) {
      print('خطأ في تحميل القراءات المعلقة: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('القراءات المعلقة'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadPendingReadings,
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadPendingReadings,
              child: _pendingReadings.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: EdgeInsets.all(16),
                      itemCount: _pendingReadings.length,
                      itemBuilder: (context, index) {
                        final reading = _pendingReadings[index];
                        return _buildPendingReadingCard(reading);
                      },
                    ),
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد قراءات معلقة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8),
          Text(
            'جميع القراءات تم مراجعتها',
            style: TextStyle(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingReadingCard(Map<String, dynamic> reading) {
    final consumption = reading['current_reading'] - reading['previous_reading'];

    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with subscriber info
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.orange[100],
                  child: Icon(Icons.person, color: Colors.orange[800]),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reading['subscriber']?['name'] ?? '',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'رقم الاشتراك: ${reading['subscriber']?['subscription_no'] ?? ''}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.orange[300]!),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.pending_actions, size: 16, color: Colors.orange[800]),
                      SizedBox(width: 4),
                      Text(
                        'معلقة',
                        style: TextStyle(
                          color: Colors.orange[800],
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Subscriber details
            _buildInfoRow('العنوان', reading['subscriber']?['address'] ?? ''),
            _buildInfoRow('المحصل', reading['collector']?['username'] ?? ''),
            _buildInfoRow('تاريخ الإدخال', _formatDate(reading['created_at'])),
            
            SizedBox(height: 16),
            
            // Reading values
            Row(
              children: [
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة السابقة',
                    reading['previous_reading'].toString(),
                    Colors.grey[600]!,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة الحالية',
                    reading['current_reading'].toString(),
                    Colors.blue[700]!,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            // Consumption
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: consumption >= 0 ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: consumption >= 0 ? Colors.green[200]! : Colors.red[200]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.flash_on,
                    color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                  ),
                  SizedBox(width: 8),
                  Text(
                    'الاستهلاك: ${consumption.toStringAsFixed(2)} كيلوواط',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                ],
              ),
            ),
            
            // Image if available
            if (reading['image_path'] != null) ...[
              SizedBox(height: 16),
              Text(
                'صورة العداد:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    'http://localhost:8000/storage/${reading['image_path']}',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.broken_image, size: 48, color: Colors.grey[400]),
                              Text('لا يمكن تحميل الصورة', style: TextStyle(color: Colors.grey[600])),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
            
            SizedBox(height: 16),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _approveReading(reading),
                    icon: Icon(Icons.check),
                    label: Text('اعتماد'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showRejectDialog(reading),
                    icon: Icon(Icons.close),
                    label: Text('رفض'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingInfo(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  Future<void> _approveReading(Map<String, dynamic> reading) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تأكيد الاعتماد'),
        content: Text('هل أنت متأكد من اعتماد قراءة ${reading['subscriber']?['name'] ?? ''}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: Text('اعتماد'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final result = await _apiService.approveReading(reading['id'], null);
      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم اعتماد القراءة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadPendingReadings();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اعتماد القراءة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في اعتماد القراءة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showRejectDialog(Map<String, dynamic> reading) {
    final _noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('رفض القراءة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('سبب رفض قراءة ${reading['subscriber']?['name'] ?? ''}:'),
            SizedBox(height: 16),
            TextFormField(
              controller: _noteController,
              decoration: InputDecoration(
                labelText: 'سبب الرفض',
                border: OutlineInputBorder(),
                hintText: 'اكتب سبب رفض هذه القراءة...',
              ),
              maxLines: 3,
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _rejectReading(reading, _noteController.text),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('رفض'),
          ),
        ],
      ),
    );
  }

  Future<void> _rejectReading(Map<String, dynamic> reading, String note) async {
    if (note.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى كتابة سبب الرفض'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      final result = await _apiService.rejectReading(reading['id'], note);
      if (result != null) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم رفض القراءة'),
            backgroundColor: Colors.orange,
          ),
        );
        _loadPendingReadings();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في رفض القراءة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في رفض القراءة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}