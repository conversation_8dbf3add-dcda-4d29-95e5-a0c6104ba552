import 'package:flutter/material.dart';
import '../../services/api_service.dart';
import '../../services/auth_service.dart';

class ReviewedReadingsScreen extends StatefulWidget {
  @override
  _ReviewedReadingsScreenState createState() => _ReviewedReadingsScreenState();
}

class _ReviewedReadingsScreenState extends State<ReviewedReadingsScreen> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  final AuthService _authService = AuthService();
  late TabController _tabController;
  
  List<dynamic> _allReviewedReadings = [];
  List<dynamic> _approvedReadings = [];
  List<dynamic> _rejectedReadings = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReviewedReadings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReviewedReadings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final user = await _authService.getCurrentUser();
      final allReadings = await _apiService.getReadings();
      
      if (allReadings != null && user != null) {
        // Filter readings reviewed by current user
        final reviewedReadings = allReadings.where((r) => 
          r['reviewed_by'] == user['id']
        ).toList();
        
        setState(() {
          _allReviewedReadings = reviewedReadings;
          _approvedReadings = reviewedReadings.where((r) => r['status'] == 'approved').toList();
          _rejectedReadings = reviewedReadings.where((r) => r['status'] == 'rejected').toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('خطأ في تحميل القراءات المراجعة: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('القراءات المراجعة'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadReviewedReadings,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: 'الكل',
              icon: Badge(
                label: Text(_allReviewedReadings.length.toString()),
                child: Icon(Icons.list),
              ),
            ),
            Tab(
              text: 'معتمدة',
              icon: Badge(
                label: Text(_approvedReadings.length.toString()),
                child: Icon(Icons.check_circle),
              ),
            ),
            Tab(
              text: 'مرفوضة',
              icon: Badge(
                label: Text(_rejectedReadings.length.toString()),
                child: Icon(Icons.cancel),
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildReadingsList(_allReviewedReadings, 'all'),
                _buildReadingsList(_approvedReadings, 'approved'),
                _buildReadingsList(_rejectedReadings, 'rejected'),
              ],
            ),
    );
  }

  Widget _buildReadingsList(List<dynamic> readings, String type) {
    if (readings.isEmpty) {
      return _buildEmptyState(type);
    }

    return RefreshIndicator(
      onRefresh: _loadReviewedReadings,
      child: ListView.builder(
        padding: EdgeInsets.all(16),
        itemCount: readings.length,
        itemBuilder: (context, index) {
          final reading = readings[index];
          return _buildReviewedReadingCard(reading);
        },
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    String message;
    IconData icon;
    
    switch (type) {
      case 'approved':
        message = 'لا توجد قراءات معتمدة';
        icon = Icons.check_circle_outline;
        break;
      case 'rejected':
        message = 'لا توجد قراءات مرفوضة';
        icon = Icons.cancel_outlined;
        break;
      default:
        message = 'لا توجد قراءات مراجعة';
        icon = Icons.history;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewedReadingCard(Map<String, dynamic> reading) {
    final isApproved = reading['status'] == 'approved';
    final statusColor = isApproved ? Colors.green : Colors.red;
    final statusText = isApproved ? 'معتمدة' : 'مرفوضة';
    final statusIcon = isApproved ? Icons.check_circle : Icons.cancel;
    final consumption = reading['current_reading'] - reading['previous_reading'];

    return Card(
      margin: EdgeInsets.only(bottom: 12),
      elevation: 3,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with subscriber name and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reading['subscriber']?['name'] ?? 'غير محدد',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'رقم الاشتراك: ${reading['subscriber']?['subscription_no'] ?? ''}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: statusColor),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 16),
            
            // Reading values
            Row(
              children: [
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة السابقة',
                    reading['previous_reading'].toString(),
                    Colors.grey[600]!,
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildReadingInfo(
                    'القراءة الحالية',
                    reading['current_reading'].toString(),
                    Colors.blue[700]!,
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            // Consumption
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: consumption >= 0 ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: consumption >= 0 ? Colors.green[200]! : Colors.red[200]!,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.flash_on,
                    color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                  ),
                  SizedBox(width: 8),
                  Text(
                    'الاستهلاك: ${consumption.toStringAsFixed(2)} كيلوواط',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color: consumption >= 0 ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                ],
              ),
            ),
            
            SizedBox(height: 12),
            
            // Details
            _buildInfoRow('المحصل', reading['collector']?['username'] ?? ''),
            _buildInfoRow('تاريخ الإدخال', _formatDate(reading['created_at'])),
            _buildInfoRow('تاريخ المراجعة', _formatDate(reading['updated_at'])),
            
            // Review note for rejected readings
            if (reading['review_note'] != null) ...[
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.note, size: 16, color: Colors.grey[600]),
                        SizedBox(width: 8),
                        Text(
                          'ملاحظة المراجعة:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[700],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      reading['review_note'],
                      style: TextStyle(
                        color: Colors.grey[800],
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Image preview if available
            if (reading['image_path'] != null) ...[
              SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.image, size: 16, color: Colors.grey[600]),
                  SizedBox(width: 4),
                  Text(
                    'صورة العداد متاحة',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  Spacer(),
                  TextButton(
                    onPressed: () => _showImageDialog(reading['image_path']),
                    child: Text('عرض الصورة'),
                  ),
                ],
              ),
            ],
            
            SizedBox(height: 16),
            
            // Action button
            Center(
              child: ElevatedButton.icon(
                onPressed: () => _showReadingDetails(reading),
                icon: Icon(Icons.info, size: 16),
                label: Text('عرض التفاصيل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingInfo(String label, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير محدد';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  void _showImageDialog(String imagePath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppBar(
              title: Text('صورة العداد'),
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            Container(
              height: 400,
              width: double.infinity,
              child: Image.network(
                'http://localhost:8000/storage/$imagePath',
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[200],
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.broken_image, size: 48, color: Colors.grey[400]),
                          Text('لا يمكن تحميل الصورة'),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showReadingDetails(Map<String, dynamic> reading) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل القراءة'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('رقم القراءة', reading['id'].toString()),
              _buildDetailRow('اسم المشترك', reading['subscriber']?['name'] ?? ''),
              _buildDetailRow('رقم الاشتراك', reading['subscriber']?['subscription_no'] ?? ''),
              _buildDetailRow('العنوان', reading['subscriber']?['address'] ?? ''),
              Divider(),
              _buildDetailRow('القراءة السابقة', reading['previous_reading'].toString()),
              _buildDetailRow('القراءة الحالية', reading['current_reading'].toString()),
              _buildDetailRow('الاستهلاك', '${(reading['current_reading'] - reading['previous_reading']).toStringAsFixed(2)} كيلوواط'),
              Divider(),
              _buildDetailRow('المحصل', reading['collector']?['username'] ?? ''),
              _buildDetailRow('تاريخ الإدخال', _formatDate(reading['created_at'])),
              _buildDetailRow('الحالة', _getStatusText(reading['status'])),
              _buildDetailRow('تاريخ المراجعة', _formatDate(reading['updated_at'])),
              if (reading['review_note'] != null)
                _buildDetailRow('ملاحظة المراجعة', reading['review_note']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'approved':
        return 'معتمدة';
      case 'rejected':
        return 'مرفوضة';
      default:
        return status;
    }
  }
}