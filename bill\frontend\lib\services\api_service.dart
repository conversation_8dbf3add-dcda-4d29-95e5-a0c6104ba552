import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'auth_service.dart';
import 'local_db_service.dart';

class ApiService {
  static const String baseUrl = 'http://10.0.2.2:8000/api';
  static const String ocrUrl = 'http://10.0.2.2:5000';
  final AuthService _authService = AuthService();
  final LocalDbService _localDbService = LocalDbService.instance;
  final Connectivity _connectivity = Connectivity();

  // Helper method for common request handling
  Future<dynamic> _handleRequestDynamic(
    Future<http.Response> request,
    String endpoint,
  ) async {
    try {
      if (!await _checkConnectivity()) return null;

      final response = await request;
      final responseBody = utf8.decode(response.bodyBytes);

      if (kDebugMode) {
        debugPrint('API Response: $endpoint | Status: ${response.statusCode}');
        if (responseBody.contains('<html') || responseBody.contains('<!DOCTYPE')) {
          debugPrint('WARNING: Received HTML response instead of JSON!');
          debugPrint('HTML preview: ${responseBody.substring(0, responseBody.length > 300 ? 300 : responseBody.length)}');
        } else if (response.statusCode >= 400) {
          debugPrint('Response body: ${responseBody.length > 200 ? responseBody.substring(0, 200) + "..." : responseBody}');
        }
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        try {
          return jsonDecode(responseBody);
        } catch (e) {
          debugPrint('JSON decode error: $e');
          debugPrint('Response body: $responseBody');
          return null;
        }
      } else {
        // Return error response as JSON for client handling
        try {
          final errorResponse = jsonDecode(responseBody);
          _logError(endpoint, response.statusCode, responseBody);
          return errorResponse; // Return error response instead of null
        } catch (e) {
          _logError(endpoint, response.statusCode, responseBody);
          return {'message': 'خطأ في الاتصال', 'status_code': response.statusCode};
        }
      }
    } catch (e) {
      _logError(endpoint, 0, e.toString());
      return null;
    }
  }

  // Helper method for Map responses (backward compatibility)
  Future<Map<String, dynamic>?> _handleRequest(
    Future<http.Response> request,
    String endpoint,
  ) async {
    final result = await _handleRequestDynamic(request, endpoint);
    if (result is Map<String, dynamic>) {
      return result;
    }
    return null;
  }

  Future<bool> _checkConnectivity() async {
    final result = await _connectivity.checkConnectivity();
    if (result == ConnectivityResult.none) {
      _logError('Connectivity', 0, 'No internet connection');
      return false;
    }
    return true;
  }

  void _logError(String endpoint, int statusCode, String error) {
    if (kDebugMode) {
      debugPrint('API Error: $endpoint | Status: $statusCode | Error: $error');
      // Log first 500 characters of error to help debug HTML responses
      if (error.length > 500) {
        debugPrint('Error preview: ${error.substring(0, 500)}...');
      }
    }
  }

  // GET request (returns dynamic to handle both Map and List responses)
  Future<dynamic> get(String endpoint) async {
    final headers = await _authService.getAuthHeaders();
    headers['Accept'] = 'application/json'; // Force JSON response

    return _handleRequestDynamic(
      http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
      ),
      'GET $endpoint',
    );
  }

  // GET request that specifically returns Map (for backward compatibility)
  Future<Map<String, dynamic>?> getMap(String endpoint) async {
    final headers = await _authService.getAuthHeaders();
    headers['Accept'] = 'application/json'; // Force JSON response

    return _handleRequest(
      http.get(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
      ),
      'GET $endpoint',
    );
  }

  // POST request
  Future<Map<String, dynamic>?> post(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    final headers = await _authService.getAuthHeaders();
    headers['Accept'] = 'application/json'; // Force JSON response

    return _handleRequest(
      http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
        body: jsonEncode(data),
      ),
      'POST $endpoint',
    );
  }

  // PUT request
  Future<Map<String, dynamic>?> put(
    String endpoint,
    Map<String, dynamic> data,
  ) async {
    final headers = await _authService.getAuthHeaders();
    headers['Accept'] = 'application/json'; // Force JSON response

    return _handleRequest(
      http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
        body: jsonEncode(data),
      ),
      'PUT $endpoint',
    );
  }

  // DELETE request
  Future<bool> delete(String endpoint) async {
    try {
      if (!await _checkConnectivity()) return false;

      final headers = await _authService.getAuthHeaders();
      headers['Accept'] = 'application/json'; // Force JSON response

      final response = await http.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      _logError('DELETE $endpoint', 0, e.toString());
      return false;
    }
  }

  // Image upload for OCR
  Future<Map<String, dynamic>?> uploadImageForOCR(File imageFile) async {
    try {
      if (!await _checkConnectivity()) return null;

      var request = http.MultipartRequest('POST', Uri.parse('$ocrUrl/ocr'));
      request.headers['Accept'] = 'application/json';
      request.files.add(await http.MultipartFile.fromPath('image', imageFile.path));

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      if (kDebugMode) {
        debugPrint('OCR Response status: ${response.statusCode}');
        if (responseData.contains('<html') || responseData.contains('<!DOCTYPE')) {
          debugPrint('WARNING: OCR returned HTML instead of JSON!');
          debugPrint('HTML preview: ${responseData.substring(0, responseData.length > 300 ? 300 : responseData.length)}');
        }
      }

      if (response.statusCode == 200) {
        try {
          return jsonDecode(responseData);
        } catch (e) {
          debugPrint('OCR JSON decode error: $e');
          return null;
        }
      }
      _logError('OCR Upload', response.statusCode, responseData);
      return null;
    } catch (e) {
      _logError('OCR Upload', 0, e.toString());
      return null;
    }
  }

  // POST with image
  Future<Map<String, dynamic>?> postWithImage(
    String endpoint,
    Map<String, dynamic> data,
    File? imageFile,
  ) async {
    try {
      if (!await _checkConnectivity()) return null;

      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl$endpoint'));

      final headers = await _authService.getAuthHeaders();
      headers['Accept'] = 'application/json'; // Force JSON response
      // Remove Content-Type for multipart requests (http package sets it automatically)
      headers.remove('Content-Type');
      request.headers.addAll(headers);

      data.forEach((key, value) {
        if (value != null) request.fields[key] = value.toString();
      });

      if (imageFile != null) {
        request.files.add(
          await http.MultipartFile.fromPath('image', imageFile.path),
        );
      }

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      if (kDebugMode) {
        debugPrint('POST with Image response status: ${response.statusCode}');
        if (responseData.contains('<html') || responseData.contains('<!DOCTYPE')) {
          debugPrint('WARNING: POST with Image returned HTML instead of JSON!');
          debugPrint('HTML preview: ${responseData.substring(0, responseData.length > 300 ? 300 : responseData.length)}');
        }
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        try {
          return jsonDecode(responseData);
        } catch (e) {
          debugPrint('POST with Image JSON decode error: $e');
          return null;
        }
      }
      _logError('POST with Image', response.statusCode, responseData);
      return null;
    } catch (e) {
      _logError('POST with Image', 0, e.toString());
      return null;
    }
  }

  // ========== Domain-Specific Methods ==========

  Future<List<dynamic>?> getZones() async {
    final result = await get('/zones');
    if (result is Map && result.containsKey('data')) {
      return result['data'];
    } else if (result is List) {
      return result;
    }
    return null;
  }

  Future<Map<String, dynamic>?> createZone(Map<String, dynamic> zoneData) async {
    return await post('/zones', zoneData);
  }

  Future<List<dynamic>?> getSubscribers({int? zoneId}) async {
    final result = await get(
      zoneId != null ? '/subscribers?zone_id=$zoneId' : '/subscribers',
    );
    if (result is Map && result.containsKey('data')) {
      return result['data'];
    } else if (result is List) {
      return result;
    }
    return null;
  }

  Future<List<dynamic>?> getReadings({String? status}) async {
    final result = await get(
      status != null ? '/readings?status=$status' : '/readings',
    );
    if (result is Map && result.containsKey('data')) {
      return result['data'];
    } else if (result is List) {
      return result;
    }
    return null;
  }

  Future<List<dynamic>?> getBills({bool? isPaid, bool? overdue}) async {
    String endpoint = '/bills';
    final params = {
      if (isPaid != null) 'is_paid': isPaid.toString(),
      if (overdue == true) 'overdue': '1',
    };
    if (params.isNotEmpty) {
      endpoint += '?${Uri(queryParameters: params).query}';
    }
    final result = await get(endpoint);
    if (result is Map && result.containsKey('data')) {
      return result['data'];
    } else if (result is List) {
      return result;
    }
    return null;
  }

  Future<List<dynamic>> getUsers() async {
    try {
      if (await _checkConnectivity()) {
        final result = await get('/users');
        if (result != null) {
          List<dynamic> usersData = [];
          if (result is Map && result.containsKey('data')) {
            usersData = result['data'] ?? [];
          } else if (result is List) {
            usersData = result;
          }

          if (usersData.isNotEmpty) {
            await _localDbService.insertUsers(
              List<Map<String, dynamic>>.from(usersData),
            );
            return usersData;
          }
        }
      }
      return await _localDbService.getUsers();
    } catch (e) {
      _logError('getUsers', 0, e.toString());
      return await _localDbService.getUsers();
    }
  }

  // إضافة قراءة جديدة
  Future<Map<String, dynamic>?> createReading(Map<String, dynamic> data, File? image) async {
    return await postWithImage('/readings', data, image);
  }

  // جلب القراءات المعلقة
  Future<List<dynamic>?> getPendingReadings() async {
    final result = await get('/readings-pending');
    if (result is Map && result.containsKey('data')) {
      return result['data'];
    } else if (result is List) {
      return result;
    }
    return null;
  }

  // اعتماد قراءة
  Future<Map<String, dynamic>?> approveReading(int readingId, String? note) async {
    return await put('/readings/$readingId/approve', {
      if (note != null) 'review_note': note,
    });
  }

  // رفض قراءة
  Future<Map<String, dynamic>?> rejectReading(int readingId, String note) async {
    return await put('/readings/$readingId/reject', {
      'review_note': note,
    });
  }

  // إضافة مشترك جديد
  Future<Map<String, dynamic>?> createSubscriber(Map<String, dynamic> data) async {
    return await post('/subscribers', data);
  }

  // تحديد الفاتورة كمدفوعة
  Future<Map<String, dynamic>?> markBillAsPaid(int billId) async {
    return await put('/bills/$billId/mark-paid', {});
  }

  // توليد الفواتير تلقائياً
  Future<Map<String, dynamic>?> generateBills() async {
    return await post('/bills/generate', {});
  }

  // إدارة المستخدمين
  Future<Map<String, dynamic>?> createUser(Map<String, dynamic> userData) async {
    return await post('/users', userData);
  }

  Future<Map<String, dynamic>?> updateUser(int userId, Map<String, dynamic> userData) async {
    return await put('/users/$userId', userData);
  }

  Future<bool> deleteUser(int userId) async {
    return await delete('/users/$userId');
  }

  Future<Map<String, dynamic>?> toggleUserStatus(int userId) async {
    return await put('/users/$userId/toggle-status', {});
  }

  Future<List<dynamic>?> getCollectors() async {
    final result = await get('/collectors');
    return result?['data'];
  }

  // إدارة المناطق - الدوال الإضافية
  Future<Map<String, dynamic>?> updateZone(int zoneId, Map<String, dynamic> zoneData) async {
    return await put('/zones/$zoneId', zoneData);
  }

  Future<bool> deleteZone(int zoneId) async {
    return await delete('/zones/$zoneId');
  }

  // إدارة المشتركين
  Future<Map<String, dynamic>?> updateSubscriber(int subscriberId, Map<String, dynamic> subscriberData) async {
    return await put('/subscribers/$subscriberId', subscriberData);
  }

  Future<bool> deleteSubscriber(int subscriberId) async {
    return await delete('/subscribers/$subscriberId');
  }

  // إعادة تعيين كلمة المرور
  Future<Map<String, dynamic>?> resetUserPassword(int userId, String password) async {
    return await put('/users/$userId/reset-password', {
      'password': password,
      'password_confirmation': password,
    });
  }


}
