import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class ConnectionTestService {
  static const String laravelUrl = 'http://********:8000';
  static const String ocrUrl = 'http://********:5000';

  /// Test basic connectivity to Laravel backend
  Future<Map<String, dynamic>> testLaravelConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$laravelUrl/api/user'),
        headers: {'Accept': 'application/json'},
      ).timeout(Duration(seconds: 10));

      return {
        'success': response.statusCode == 401, // 401 is expected without auth
        'status_code': response.statusCode,
        'message': response.statusCode == 401
            ? 'Laravel API is running (authentication required)'
            : 'Unexpected response: ${response.statusCode}',
        'response_body': response.body.length > 200
            ? '${response.body.substring(0, 200)}...'
            : response.body,
      };
    } catch (e) {
      return {
        'success': false,
        'status_code': 0,
        'message': 'Connection failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test OCR service connection
  Future<Map<String, dynamic>> testOcrConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$ocrUrl/health'),
      ).timeout(Duration(seconds: 10));

      return {
        'success': response.statusCode == 200,
        'status_code': response.statusCode,
        'message': response.statusCode == 200
            ? 'OCR service is running'
            : 'OCR service error: ${response.statusCode}',
        'response_body': response.body,
      };
    } catch (e) {
      return {
        'success': false,
        'status_code': 0,
        'message': 'OCR connection failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test login endpoint
  Future<Map<String, dynamic>> testLogin() async {
    try {
      final response = await http.post(
        Uri.parse('$laravelUrl/api/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'username': 'admin',
          'password': 'password',
        }),
      ).timeout(Duration(seconds: 15));

      final responseData = jsonDecode(response.body);

      return {
        'success': response.statusCode == 200,
        'status_code': response.statusCode,
        'message': response.statusCode == 200
            ? 'Login successful'
            : 'Login failed: ${responseData['message'] ?? 'Unknown error'}',
        'has_token': response.statusCode == 200 && responseData['access_token'] != null,
        'user_role': response.statusCode == 200 && responseData['user'] != null
            ? responseData['user']['role']
            : null,
        'response_body': response.body,
      };
    } catch (e) {
      return {
        'success': false,
        'status_code': 0,
        'message': 'Login test failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test authenticated endpoint
  Future<Map<String, dynamic>> testAuthenticatedEndpoint(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$laravelUrl/api/user'),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      ).timeout(Duration(seconds: 10));

      return {
        'success': response.statusCode == 200,
        'status_code': response.statusCode,
        'message': response.statusCode == 200
            ? 'Authenticated request successful'
            : 'Authentication failed: ${response.statusCode}',
        'response_body': response.body,
      };
    } catch (e) {
      return {
        'success': false,
        'status_code': 0,
        'message': 'Authenticated test failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test API endpoints that the app uses
  Future<Map<String, dynamic>> testApiEndpoints(String token) async {
    final endpoints = [
      '/api/zones',
      '/api/subscribers',
      '/api/readings',
      '/api/bills',
      '/api/readings-pending',
    ];

    Map<String, dynamic> results = {};

    for (String endpoint in endpoints) {
      try {
        final response = await http.get(
          Uri.parse('$laravelUrl$endpoint'),
          headers: {
            'Authorization': 'Bearer $token',
            'Accept': 'application/json',
          },
        ).timeout(Duration(seconds: 10));

        results[endpoint] = {
          'success': response.statusCode == 200,
          'status_code': response.statusCode,
          'message': response.statusCode == 200
              ? 'OK'
              : 'Error: ${response.statusCode}',
        };
      } catch (e) {
        results[endpoint] = {
          'success': false,
          'status_code': 0,
          'message': 'Failed: ${e.toString()}',
        };
      }
    }

    return results;
  }

  /// Run comprehensive connection test
  Future<Map<String, dynamic>> runFullConnectionTest() async {
    Map<String, dynamic> results = {
      'timestamp': DateTime.now().toIso8601String(),
      'tests': {},
    };

    // Test 1: Laravel basic connection
    debugPrint('Testing Laravel connection...');
    results['tests']['laravel_connection'] = await testLaravelConnection();

    // Test 2: OCR service connection
    debugPrint('Testing OCR connection...');
    results['tests']['ocr_connection'] = await testOcrConnection();

    // Test 3: Login functionality
    debugPrint('Testing login...');
    final loginResult = await testLogin();
    results['tests']['login'] = loginResult;

    // Test 4: Authenticated endpoints (if login successful)
    if (loginResult['success'] && loginResult['has_token']) {
      final token = jsonDecode(loginResult['response_body'])['access_token'];

      debugPrint('Testing authenticated endpoint...');
      results['tests']['authenticated_request'] = await testAuthenticatedEndpoint(token);

      debugPrint('Testing API endpoints...');
      results['tests']['api_endpoints'] = await testApiEndpoints(token);
    }

    // Calculate overall success
    int successCount = 0;
    int totalTests = 0;

    void countTests(Map<String, dynamic> testResults) {
      testResults.forEach((key, value) {
        if (value is Map && value.containsKey('success')) {
          totalTests++;
          if (value['success'] == true) successCount++;
        } else if (value is Map<String, dynamic>) {
          countTests(value);
        }
      });
    }

    countTests(results['tests']);

    results['summary'] = {
      'total_tests': totalTests,
      'successful_tests': successCount,
      'success_rate': totalTests > 0 ? (successCount / totalTests * 100).round() : 0,
      'overall_status': successCount == totalTests ? 'ALL_PASS' :
                       successCount > totalTests / 2 ? 'PARTIAL_PASS' : 'FAIL',
    };

    return results;
  }

  /// Get connection recommendations based on test results
  List<String> getConnectionRecommendations(Map<String, dynamic> testResults) {
    List<String> recommendations = [];

    final laravelTest = testResults['tests']?['laravel_connection'];
    final ocrTest = testResults['tests']?['ocr_connection'];
    final loginTest = testResults['tests']?['login'];

    if (laravelTest?['success'] != true) {
      recommendations.add('تأكد من تشغيل خادم Laravel على المنفذ 8000');
      recommendations.add('تحقق من أن عنوان IP صحيح (******** للمحاكي)');
      recommendations.add('تأكد من إعدادات CORS في Laravel');
    }

    if (ocrTest?['success'] != true) {
      recommendations.add('تأكد من تشغيل خدمة OCR على المنفذ 5000');
      recommendations.add('تحقق من تثبيت متطلبات Python للـ OCR');
    }

    if (loginTest?['success'] != true) {
      recommendations.add('تحقق من بيانات تسجيل الدخول (admin/password)');
      recommendations.add('تأكد من تشغيل قاعدة البيانات وتنفيذ الـ migrations');
      recommendations.add('تحقق من إعدادات Sanctum في Laravel');
    }

    if (recommendations.isEmpty) {
      recommendations.add('جميع الاتصالات تعمل بشكل صحيح! 🎉');
    }

    return recommendations;
  }
}
