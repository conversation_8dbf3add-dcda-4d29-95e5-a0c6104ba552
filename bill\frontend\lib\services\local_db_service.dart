import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';

class LocalDbService {
  static const String _dbName = 'electric_bill_app.db';
  static const int _dbVersion = 2; // Increment when schema changes
  static Database? _database;

  // Prevent direct instantiation
  LocalDbService._privateConstructor();
  static final LocalDbService instance = LocalDbService._privateConstructor();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      final dbPath = await getDatabasesPath();
      final path = join(dbPath, _dbName);

      return await openDatabase(
        path,
        version: _dbVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onConfigure: _onConfigure,
      );
    } catch (e) {
      debugPrint('Error initializing database: $e');
      rethrow;
    }
  }

  Future<void> _onConfigure(Database db) async {
    await db.execute('PRAGMA foreign_keys = ON');
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.transaction((txn) async {
      // Users table
      await txn.execute('''
        CREATE TABLE users (
          id INTEGER PRIMARY KEY,
          username TEXT UNIQUE NOT NULL,
          name TEXT,
          phone TEXT,
          role TEXT CHECK(role IN ('admin', 'collector', 'reviewer')) NOT NULL,
          area TEXT,
          status TEXT DEFAULT 'active',
          last_sync TIMESTAMP
        )
      ''');

      // Zones table
      await txn.execute('''
        CREATE TABLE zones (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL,
          collector_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
          last_updated TIMESTAMP
        )
      ''');

      // Subscribers table
      await txn.execute('''
        CREATE TABLE subscribers (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL,
          subscription_no TEXT UNIQUE NOT NULL,
          address TEXT,
          zone_id INTEGER REFERENCES zones(id) ON DELETE CASCADE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      ''');

      // Create indexes for better performance
      await txn.execute('CREATE INDEX idx_subscribers_zone ON subscribers(zone_id)');
    });
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await db.execute('ALTER TABLE users ADD COLUMN last_sync TIMESTAMP');
    }
  }

  // ========== User Operations ==========
  Future<List<Map<String, dynamic>>> getUsers() async {
    final db = await database;
    return await db.query('users', orderBy: 'name');
  }

  Future<int> insertUsers(List<Map<String, dynamic>> users) async {
    final db = await database;
    return await db.transaction((txn) async {
      int count = 0;
      for (final user in users) {
        try {
          await txn.insert(
            'users',
            user,
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
          count++;
        } catch (e) {
          debugPrint('Error inserting user ${user['id']}: $e');
        }
      }
      return count;
    });
  }

  Future<Map<String, dynamic>?> getUserById(int id) async {
    final db = await database;
    final results = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }

  // ========== Zone Operations ==========
  Future<List<Map<String, dynamic>>> getZones() async {
    final db = await database;
    return await db.query('zones', orderBy: 'name');
  }

  // ========== Subscriber Operations ==========
  Future<List<Map<String, dynamic>>> getSubscribers({int? zoneId}) async {
    final db = await database;
    return await db.query(
      'subscribers',
      where: zoneId != null ? 'zone_id = ?' : null,
      whereArgs: zoneId != null ? [zoneId] : null,
      orderBy: 'name',
    );
  }

  // ========== Maintenance ==========
  Future<void> clearDatabase() async {
    final db = await database;
    await db.transaction((txn) async {
      await txn.delete('users');
      await txn.delete('zones');
      await txn.delete('subscribers');
    });
  }

  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
