<?php

// Simple API test script
echo "=== Testing Laravel API Endpoints ===\n\n";

$baseUrl = 'http://127.0.0.1:8000';

function testEndpoint($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    $defaultHeaders = ['Accept: application/json'];
    if ($data && ($method === 'POST' || $method === 'PUT')) {
        $defaultHeaders[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge($defaultHeaders, $headers));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => !$error && $httpCode < 400,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

// Test 1: Basic API endpoint
echo "1. Testing basic API endpoint...\n";
$result = testEndpoint($baseUrl . '/api/user');
echo "   HTTP Code: " . $result['http_code'] . "\n";
if ($result['http_code'] == 401) {
    echo "   ✅ API is working (401 expected without auth)\n";
} else {
    echo "   ❌ Unexpected response\n";
    echo "   Response: " . substr($result['response'], 0, 200) . "\n";
}
echo "\n";

// Test 2: Login endpoint
echo "2. Testing login endpoint...\n";
$loginData = [
    'username' => 'admin',
    'password' => 'password'
];

$result = testEndpoint($baseUrl . '/api/login', 'POST', $loginData);
echo "   HTTP Code: " . $result['http_code'] . "\n";

if ($result['http_code'] == 200) {
    $responseData = json_decode($result['response'], true);
    if (isset($responseData['access_token'])) {
        echo "   ✅ Login successful\n";
        echo "   User role: " . ($responseData['user']['role'] ?? 'unknown') . "\n";
        
        $token = $responseData['access_token'];
        
        // Test 3: Authenticated request
        echo "\n3. Testing authenticated user endpoint...\n";
        $authResult = testEndpoint(
            $baseUrl . '/api/user', 
            'GET', 
            null, 
            ['Authorization: Bearer ' . $token]
        );
        echo "   HTTP Code: " . $authResult['http_code'] . "\n";
        if ($authResult['http_code'] == 200) {
            echo "   ✅ Authentication working\n";
        } else {
            echo "   ❌ Authentication failed\n";
        }
        
        // Test 4: Users endpoint
        echo "\n4. Testing users endpoint...\n";
        $usersResult = testEndpoint(
            $baseUrl . '/api/users', 
            'GET', 
            null, 
            ['Authorization: Bearer ' . $token]
        );
        echo "   HTTP Code: " . $usersResult['http_code'] . "\n";
        if ($usersResult['http_code'] == 200) {
            echo "   ✅ Users endpoint working\n";
            $users = json_decode($usersResult['response'], true);
            echo "   Users found: " . (count($users['data'] ?? []) ?? 0) . "\n";
        } else {
            echo "   ❌ Users endpoint failed\n";
            echo "   Response: " . substr($usersResult['response'], 0, 200) . "\n";
        }
        
        // Test 5: Create user endpoint
        echo "\n5. Testing create user endpoint...\n";
        $newUserData = [
            'username' => 'testuser_' . time(),
            'name' => 'Test User',
            'password' => 'password123',
            'role' => 'collector',
            'phone' => '1234567890'
        ];
        
        $createResult = testEndpoint(
            $baseUrl . '/api/users', 
            'POST', 
            $newUserData, 
            ['Authorization: Bearer ' . $token]
        );
        echo "   HTTP Code: " . $createResult['http_code'] . "\n";
        if ($createResult['http_code'] == 201) {
            echo "   ✅ User creation successful\n";
        } else {
            echo "   ❌ User creation failed\n";
            echo "   Response: " . substr($createResult['response'], 0, 300) . "\n";
        }
        
    } else {
        echo "   ❌ Login response missing token\n";
        echo "   Response: " . substr($result['response'], 0, 200) . "\n";
    }
} else {
    echo "   ❌ Login failed\n";
    echo "   Response: " . substr($result['response'], 0, 200) . "\n";
}

echo "\n=== Test Complete ===\n";
echo "If all tests pass, your API is ready for Flutter integration.\n";

?>
