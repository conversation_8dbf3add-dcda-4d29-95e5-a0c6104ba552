<?php

// Simple script to test <PERSON><PERSON> backend endpoints
echo "=== Testing Laravel Backend Connection ===\n\n";

$baseUrl = 'http://localhost:8000/api';

// Test 1: Basic API endpoint
echo "1. Testing basic API endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/user');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "   ❌ Connection failed: $error\n";
} else {
    echo "   ✅ HTTP Status: $httpCode\n";
    if ($httpCode == 401) {
        echo "   ✅ API is running (401 expected without auth)\n";
    } else {
        echo "   ⚠️  Unexpected status code\n";
    }
}

echo "\n";

// Test 2: Login endpoint
echo "2. Testing login endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'username' => 'admin',
    'password' => 'password'
]));
curl_setopt($ch, CURLOPT_TIMEOUT, 15);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "   ❌ Login test failed: $error\n";
} else {
    echo "   ✅ HTTP Status: $httpCode\n";
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if (isset($data['access_token'])) {
            echo "   ✅ Login successful, token received\n";
            echo "   ✅ User role: " . ($data['user']['role'] ?? 'unknown') . "\n";
            
            // Test 3: Authenticated request
            echo "\n3. Testing authenticated request...\n";
            $token = $data['access_token'];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl . '/user');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $token,
                'Accept: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $authResponse = curl_exec($ch);
            $authHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $authError = curl_error($ch);
            curl_close($ch);
            
            if ($authError) {
                echo "   ❌ Authenticated request failed: $authError\n";
            } else {
                echo "   ✅ Authenticated request HTTP Status: $authHttpCode\n";
                if ($authHttpCode == 200) {
                    echo "   ✅ Authentication working correctly\n";
                } else {
                    echo "   ❌ Authentication failed\n";
                }
            }
            
        } else {
            echo "   ❌ Login response missing token\n";
        }
    } else {
        echo "   ❌ Login failed\n";
        echo "   Response: " . substr($response, 0, 200) . "\n";
    }
}

echo "\n";

// Test 4: CORS headers
echo "4. Testing CORS configuration...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/user');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'OPTIONS');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Origin: http://localhost:3000',
    'Access-Control-Request-Method: GET',
    'Access-Control-Request-Headers: Authorization'
]);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if (strpos($response, 'Access-Control-Allow-Origin') !== false) {
    echo "   ✅ CORS headers present\n";
} else {
    echo "   ⚠️  CORS headers may be missing\n";
}

echo "\n=== Test Summary ===\n";
echo "If all tests pass, the backend is ready for Flutter connection.\n";
echo "If tests fail, check:\n";
echo "- Laravel server is running (php artisan serve)\n";
echo "- Database is set up and migrated\n";
echo "- Default users are seeded\n";
echo "- CORS is configured properly\n";

?>
