<?php

echo "=== Simple Laravel API Test ===\n\n";

// Test 1: Basic connectivity
echo "1. Testing basic connectivity...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8000/api-test');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200) {
    echo "   ✅ Laravel is running\n";
    echo "   Response: " . substr($response, 0, 100) . "\n";
} else {
    echo "   ❌ Laravel not responding properly\n";
    echo "   HTTP Code: $httpCode\n";
}

echo "\n";

// Test 2: API user endpoint (should return 401)
echo "2. Testing API user endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8000/api/user');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 401) {
    echo "   ✅ API endpoint working (401 expected)\n";
} else {
    echo "   ❌ Unexpected response\n";
    echo "   HTTP Code: $httpCode\n";
    echo "   Response: " . substr($response, 0, 200) . "\n";
}

echo "\n=== Test Complete ===\n";

?>
