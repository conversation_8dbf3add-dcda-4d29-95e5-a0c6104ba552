<?php

namespace Tests\Unit;

use App\Models\User;
use App\Enums\UserRole;
use PHPUnit\Framework\TestCase;

class UserRoleTest extends TestCase
{
    public function test_user_role_constants_match_enum_values()
    {
        $this->assertEquals(UserRole::ADMIN->value, User::ROLE_ADMIN);
        $this->assertEquals(UserRole::COLLECTOR->value, User::ROLE_COLLECTOR);
        $this->assertEquals(UserRole::REVIEWER->value, User::ROLE_REVIEWER);
    }

    public function test_user_role_enum_arabic_names()
    {
        $this->assertEquals('مدير', UserRole::ADMIN->getArabicName());
        $this->assertEquals('محصل', UserRole::COLLECTOR->getArabicName());
        $this->assertEquals('مراجع', UserRole::REVIEWER->getArabicName());
    }

    public function test_user_role_permissions()
    {
        // Test admin permissions
        $this->assertTrue(UserRole::ADMIN->isAdmin());
        $this->assertTrue(UserRole::ADMIN->canManageZones());
        $this->assertTrue(UserRole::ADMIN->canManageSubscribers());
        $this->assertTrue(UserRole::ADMIN->canViewBills());
        $this->assertTrue(UserRole::ADMIN->canMarkBillsAsPaid());
        $this->assertFalse(UserRole::ADMIN->canCreateReadings());
        $this->assertFalse(UserRole::ADMIN->canReviewReadings());
        $this->assertFalse(UserRole::ADMIN->canCreateBills());

        // Test collector permissions
        $this->assertFalse(UserRole::COLLECTOR->isAdmin());
        $this->assertFalse(UserRole::COLLECTOR->canManageZones());
        $this->assertFalse(UserRole::COLLECTOR->canManageSubscribers());
        $this->assertTrue(UserRole::COLLECTOR->canViewBills());
        $this->assertTrue(UserRole::COLLECTOR->canMarkBillsAsPaid());
        $this->assertTrue(UserRole::COLLECTOR->canCreateReadings());
        $this->assertFalse(UserRole::COLLECTOR->canReviewReadings());
        $this->assertFalse(UserRole::COLLECTOR->canCreateBills());

        // Test reviewer permissions
        $this->assertFalse(UserRole::REVIEWER->isAdmin());
        $this->assertFalse(UserRole::REVIEWER->canManageZones());
        $this->assertFalse(UserRole::REVIEWER->canManageSubscribers());
        $this->assertTrue(UserRole::REVIEWER->canViewBills());
        $this->assertFalse(UserRole::REVIEWER->canMarkBillsAsPaid());
        $this->assertFalse(UserRole::REVIEWER->canCreateReadings());
        $this->assertTrue(UserRole::REVIEWER->canReviewReadings());
        $this->assertTrue(UserRole::REVIEWER->canCreateBills());
    }

    public function test_user_role_values_method()
    {
        $expectedValues = ['admin', 'collector', 'reviewer'];
        $this->assertEquals($expectedValues, UserRole::values());
    }

    public function test_user_role_arabic_names_mapping()
    {
        $expectedMapping = [
            'admin' => 'مدير',
            'collector' => 'محصل',
            'reviewer' => 'مراجع',
        ];
        $this->assertEquals($expectedMapping, UserRole::getArabicNames());
    }
}
