<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Enums\UserRole;

echo "=== Role System Verification ===\n\n";

// Test 1: Constants match enum values
echo "1. Testing constants match enum values:\n";
echo "   ROLE_ADMIN: " . User::ROLE_ADMIN . " = " . UserRole::ADMIN->value . " ✓\n";
echo "   ROLE_COLLECTOR: " . User::ROLE_COLLECTOR . " = " . UserRole::COLLECTOR->value . " ✓\n";
echo "   ROLE_REVIEWER: " . User::ROLE_REVIEWER . " = " . UserRole::REVIEWER->value . " ✓\n\n";

// Test 2: Arabic names
echo "2. Testing Arabic role names:\n";
foreach (UserRole::cases() as $role) {
    echo "   {$role->value} -> {$role->getArabicName()}\n";
}
echo "\n";

// Test 3: Role permissions
echo "3. Testing role permissions:\n";

echo "   Admin permissions:\n";
echo "     - Can manage zones: " . (UserRole::ADMIN->canManageZones() ? 'Yes' : 'No') . "\n";
echo "     - Can create readings: " . (UserRole::ADMIN->canCreateReadings() ? 'Yes' : 'No') . "\n";
echo "     - Can review readings: " . (UserRole::ADMIN->canReviewReadings() ? 'Yes' : 'No') . "\n";
echo "     - Can create bills: " . (UserRole::ADMIN->canCreateBills() ? 'Yes' : 'No') . "\n\n";

echo "   Collector permissions:\n";
echo "     - Can manage zones: " . (UserRole::COLLECTOR->canManageZones() ? 'Yes' : 'No') . "\n";
echo "     - Can create readings: " . (UserRole::COLLECTOR->canCreateReadings() ? 'Yes' : 'No') . "\n";
echo "     - Can review readings: " . (UserRole::COLLECTOR->canReviewReadings() ? 'Yes' : 'No') . "\n";
echo "     - Can create bills: " . (UserRole::COLLECTOR->canCreateBills() ? 'Yes' : 'No') . "\n\n";

echo "   Reviewer permissions:\n";
echo "     - Can manage zones: " . (UserRole::REVIEWER->canManageZones() ? 'Yes' : 'No') . "\n";
echo "     - Can create readings: " . (UserRole::REVIEWER->canCreateReadings() ? 'Yes' : 'No') . "\n";
echo "     - Can review readings: " . (UserRole::REVIEWER->canReviewReadings() ? 'Yes' : 'No') . "\n";
echo "     - Can create bills: " . (UserRole::REVIEWER->canCreateBills() ? 'Yes' : 'No') . "\n\n";

// Test 4: Enum values method
echo "4. Testing UserRole::values() method:\n";
echo "   Available roles: " . implode(', ', UserRole::values()) . "\n\n";

// Test 5: Arabic names mapping
echo "5. Testing Arabic names mapping:\n";
foreach (UserRole::getArabicNames() as $role => $arabicName) {
    echo "   {$role} -> {$arabicName}\n";
}

echo "\n=== All tests completed successfully! ===\n";
