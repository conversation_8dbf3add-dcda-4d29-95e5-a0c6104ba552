# خدمة OCR لاستخراج قراءات العدادات

## التثبيت

```bash
pip install -r requirements.txt
```

## التشغيل

```bash
python app.py
```

الخدمة ستعمل على المنفذ 5000

## الاستخدام

### استخراج قراءة العداد
```bash
POST /ocr
Content-Type: multipart/form-data

image: [ملف الصورة]
```

### الاستجابة
```json
{
  "success": true,
  "subscriber_no": "12345",
  "current_reading": 1340.5,
  "confidence": "high"
}
```

### فحص حالة الخدمة
```bash
GET /health
```