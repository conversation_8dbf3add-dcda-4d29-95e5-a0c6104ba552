from flask import Flask, request, jsonify
from flask_cors import CORS
import cv2
import numpy as np
import easyocr
import re
import os
from werkzeug.utils import secure_filename
import tempfile
import logging
from datetime import datetime
import json

app = Flask(__name__)
CORS(app)  # تمكين CORS للسماح بطلبات من Laravel

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إعداد EasyOCR
reader = easyocr.Reader(['en', 'ar'])

# إعدادات رفع الملفات
UPLOAD_FOLDER = 'temp_images'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def preprocess_image(image_path):
    """تحسين الصورة لتحسين دقة OCR"""
    try:
        # قراءة الصورة
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError("لا يمكن قراءة الصورة")

        # تحويل إلى رمادي
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # تحسين التباين
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # تطبيق فلتر لتقليل الضوضاء
        denoised = cv2.medianBlur(enhanced, 5)

        # تحسين الحواف
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)

        # تطبيق threshold لتحسين وضوح النص
        _, thresh = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    except Exception as e:
        logger.error(f"خطأ في معالجة الصورة: {str(e)}")
        raise

def extract_numbers_from_text(text):
    """استخراج الأرقام من النص"""
    try:
        # تنظيف النص
        cleaned_text = re.sub(r'[^\d\.\s]', ' ', text)

        # البحث عن أرقام (بما في ذلك الأرقام العشرية)
        number_patterns = [
            r'\d+\.\d+',  # أرقام عشرية
            r'\d{4,}',    # أرقام طويلة (4 أرقام أو أكثر)
            r'\d+',       # أي رقم
        ]

        numbers = []
        for pattern in number_patterns:
            matches = re.findall(pattern, cleaned_text)
            for match in matches:
                try:
                    num = float(match)
                    if num not in numbers and num > 0:
                        numbers.append(num)
                except ValueError:
                    continue

        return sorted(numbers, reverse=True)

    except Exception as e:
        logger.error(f"خطأ في استخراج الأرقام: {str(e)}")
        return []

def find_meter_reading(image_path):
    """استخراج قراءة العداد من الصورة"""
    try:
        logger.info(f"بدء معالجة الصورة: {image_path}")

        # تحسين الصورة
        processed_image = preprocess_image(image_path)

        # حفظ الصورة المحسنة مؤقتاً
        temp_path = tempfile.mktemp(suffix='.jpg')
        cv2.imwrite(temp_path, processed_image)

        # استخراج النص باستخدام EasyOCR مع إعدادات محسنة
        results = reader.readtext(
            temp_path,
            detail=1,
            paragraph=False,
            width_ths=0.7,
            height_ths=0.7
        )

        # تنظيف الملف المؤقت
        if os.path.exists(temp_path):
            os.remove(temp_path)

        # استخراج النص مع مستوى الثقة
        text_results = []
        for (bbox, text, confidence) in results:
            if confidence > 0.5:  # فقط النصوص عالية الثقة
                text_results.append({
                    'text': text,
                    'confidence': confidence,
                    'bbox': bbox
                })

        all_text = ' '.join([result['text'] for result in text_results])
        logger.info(f"النص المستخرج: {all_text}")

        numbers = extract_numbers_from_text(all_text)
        logger.info(f"الأرقام المستخرجة: {numbers}")

        if numbers:
            # تحليل ذكي للأرقام
            current_reading = None
            subscription_no = None

            # البحث عن قراءة العداد (عادة الرقم الأكبر أو الأطول)
            for num in numbers:
                num_str = str(int(num)) if num.is_integer() else str(num)

                # قراءة العداد عادة تكون رقم كبير (أكثر من 100)
                if num >= 100 and current_reading is None:
                    current_reading = num

                # رقم الاشتراك عادة يكون 4-8 أرقام
                elif len(num_str) >= 4 and len(num_str) <= 8 and subscription_no is None:
                    subscription_no = num_str

            # إذا لم نجد قراءة، استخدم أكبر رقم
            if current_reading is None and numbers:
                current_reading = max(numbers)

            confidence_level = 'high' if len(text_results) > 2 else 'medium'

            result = {
                'current_reading': current_reading,
                'subscription_no': subscription_no,
                'all_numbers': numbers,
                'raw_text': all_text,
                'confidence': confidence_level,
                'ocr_results': text_results
            }

            logger.info(f"النتيجة النهائية: {result}")
            return result
        else:
            logger.warning("لم يتم العثور على أرقام في الصورة")
            return None

    except Exception as e:
        logger.error(f"خطأ في معالجة الصورة: {str(e)}")
        return None

@app.route('/ocr', methods=['POST'])
def process_meter_image():
    """معالجة صورة العداد واستخراج القراءة"""
    start_time = datetime.now()

    try:
        # التحقق من وجود الصورة
        if 'image' not in request.files:
            return jsonify({
                'success': False,
                'error': 'لم يتم إرسال صورة',
                'error_code': 'NO_IMAGE'
            }), 400

        file = request.files['image']

        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'لم يتم اختيار ملف',
                'error_code': 'EMPTY_FILENAME'
            }), 400

        # التحقق من حجم الملف
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > MAX_FILE_SIZE:
            return jsonify({
                'success': False,
                'error': f'حجم الملف كبير جداً. الحد الأقصى {MAX_FILE_SIZE // (1024*1024)}MB',
                'error_code': 'FILE_TOO_LARGE'
            }), 400

        if file and allowed_file(file.filename):
            try:
                # إنشاء اسم ملف فريد
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{timestamp}_{secure_filename(file.filename)}"
                temp_path = os.path.join(UPLOAD_FOLDER, filename)

                # حفظ الملف
                file.save(temp_path)
                logger.info(f"تم حفظ الملف: {temp_path}")

                # معالجة الصورة
                result = find_meter_reading(temp_path)

                # حذف الملف المؤقت
                if os.path.exists(temp_path):
                    os.remove(temp_path)

                processing_time = (datetime.now() - start_time).total_seconds()

                if result:
                    response_data = {
                        'success': True,
                        'data': {
                            'subscriber_no': result['subscription_no'],
                            'current_reading': result['current_reading'],
                            'confidence': result['confidence']
                        },
                        'metadata': {
                            'processing_time': processing_time,
                            'timestamp': datetime.now().isoformat(),
                            'file_size': file_size
                        },
                        'debug_info': {
                            'all_numbers': result['all_numbers'],
                            'raw_text': result['raw_text'],
                            'ocr_results_count': len(result.get('ocr_results', []))
                        }
                    }

                    logger.info(f"معالجة ناجحة في {processing_time:.2f} ثانية")
                    return jsonify(response_data)
                else:
                    return jsonify({
                        'success': False,
                        'error': 'لم يتم العثور على أرقام واضحة في الصورة',
                        'error_code': 'NO_NUMBERS_FOUND',
                        'metadata': {
                            'processing_time': processing_time,
                            'timestamp': datetime.now().isoformat()
                        }
                    }), 422

            except Exception as e:
                logger.error(f"خطأ في معالجة الصورة: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': f'خطأ في معالجة الصورة: {str(e)}',
                    'error_code': 'PROCESSING_ERROR'
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': 'نوع الملف غير مدعوم. الأنواع المدعومة: ' + ', '.join(ALLOWED_EXTENSIONS),
                'error_code': 'UNSUPPORTED_FILE_TYPE'
            }), 400

    except Exception as e:
        logger.error(f"خطأ عام في الخدمة: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'خطأ داخلي في الخدمة',
            'error_code': 'INTERNAL_ERROR'
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """فحص حالة الخدمة"""
    try:
        # اختبار EasyOCR
        test_result = reader.readtext(np.zeros((100, 100, 3), dtype=np.uint8))

        return jsonify({
            'status': 'healthy',
            'service': 'OCR Service for Electricity Meters',
            'version': '2.0.0',
            'timestamp': datetime.now().isoformat(),
            'capabilities': {
                'languages': ['en', 'ar'],
                'supported_formats': list(ALLOWED_EXTENSIONS),
                'max_file_size_mb': MAX_FILE_SIZE // (1024*1024)
            },
            'ocr_engine': 'EasyOCR',
            'status_details': {
                'reader_initialized': True,
                'temp_folder_exists': os.path.exists(UPLOAD_FOLDER)
            }
        })
    except Exception as e:
        logger.error(f"خطأ في فحص الصحة: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/test', methods=['GET'])
def test_endpoint():
    """اختبار بسيط للخدمة"""
    return jsonify({
        'message': 'خدمة OCR تعمل بشكل صحيح',
        'timestamp': datetime.now().isoformat(),
        'endpoints': {
            'POST /ocr': 'معالجة صورة العداد',
            'GET /health': 'فحص حالة الخدمة',
            'GET /test': 'اختبار الخدمة'
        }
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': 'الصفحة غير موجودة',
        'error_code': 'NOT_FOUND'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': 'خطأ داخلي في الخدمة',
        'error_code': 'INTERNAL_ERROR'
    }), 500

if __name__ == '__main__':
    logger.info("بدء تشغيل خدمة OCR...")
    logger.info(f"المجلد المؤقت: {UPLOAD_FOLDER}")
    logger.info(f"الحد الأقصى لحجم الملف: {MAX_FILE_SIZE // (1024*1024)}MB")

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,  # تعطيل debug في الإنتاج
        threaded=True
    )