from flask import Flask, request, jsonify
import cv2
import numpy as np
import easyocr
import re
import os
from werkzeug.utils import secure_filename
import tempfile

app = Flask(__name__)

# إعداد EasyOCR
reader = easyocr.Reader(['en', 'ar'])

# إعدادات رفع الملفات
UPLOAD_FOLDER = 'temp_images'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def preprocess_image(image_path):
    """تحسين الصورة لتحسين دقة OCR"""
    # قراءة الصورة
    image = cv2.imread(image_path)
    
    # تحويل إلى رمادي
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # تحسين التباين
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    
    # تطبيق فلتر لتقليل الضوضاء
    denoised = cv2.medianBlur(enhanced, 3)
    
    # تحسين الحواف
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(denoised, -1, kernel)
    
    return sharpened

def extract_numbers_from_text(text):
    """استخراج الأرقام من النص"""
    # البحث عن أرقام (بما في ذلك الأرقام العشرية)
    numbers = re.findall(r'\d+\.?\d*', text)
    return [float(num) for num in numbers if num]

def find_meter_reading(image_path):
    """استخراج قراءة العداد من الصورة"""
    try:
        # تحسين الصورة
        processed_image = preprocess_image(image_path)
        
        # حفظ الصورة المحسنة مؤقتاً
        temp_path = tempfile.mktemp(suffix='.jpg')
        cv2.imwrite(temp_path, processed_image)
        
        # استخراج النص باستخدام EasyOCR
        results = reader.readtext(temp_path)
        
        # تنظيف الملف المؤقت
        os.remove(temp_path)
        
        all_text = ' '.join([result[1] for result in results])
        numbers = extract_numbers_from_text(all_text)
        
        # البحث عن أكبر رقم (عادة ما يكون قراءة العداد)
        if numbers:
            # ترتيب الأرقام تنازلياً واختيار الأكبر
            numbers.sort(reverse=True)
            current_reading = numbers[0]
            
            # البحث عن رقم الاشتراك (عادة ما يكون رقم أصغر)
            subscription_no = None
            for num in numbers:
                if num != current_reading and len(str(int(num))) >= 4:
                    subscription_no = str(int(num))
                    break
            
            return {
                'current_reading': current_reading,
                'subscription_no': subscription_no,
                'all_numbers': numbers,
                'raw_text': all_text
            }
        else:
            return None
            
    except Exception as e:
        print(f"خطأ في معالجة الصورة: {str(e)}")
        return None

@app.route('/ocr', methods=['POST'])
def process_meter_image():
    """معالجة صورة العداد واستخراج القراءة"""
    
    if 'image' not in request.files:
        return jsonify({'error': 'لم يتم إرسال صورة'}), 400
    
    file = request.files['image']
    
    if file.filename == '':
        return jsonify({'error': 'لم يتم اختيار ملف'}), 400
    
    if file and allowed_file(file.filename):
        try:
            # حفظ الملف مؤقتاً
            filename = secure_filename(file.filename)
            temp_path = os.path.join(UPLOAD_FOLDER, filename)
            file.save(temp_path)
            
            # معالجة الصورة
            result = find_meter_reading(temp_path)
            
            # حذف الملف المؤقت
            os.remove(temp_path)
            
            if result:
                return jsonify({
                    'success': True,
                    'subscriber_no': result['subscription_no'],
                    'current_reading': result['current_reading'],
                    'confidence': 'high' if result['subscription_no'] else 'medium',
                    'debug_info': {
                        'all_numbers': result['all_numbers'],
                        'raw_text': result['raw_text']
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'لم يتم العثور على أرقام في الصورة'
                }), 422
                
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'خطأ في معالجة الصورة: {str(e)}'
            }), 500
    
    return jsonify({'error': 'نوع الملف غير مدعوم'}), 400

@app.route('/health', methods=['GET'])
def health_check():
    """فحص حالة الخدمة"""
    return jsonify({
        'status': 'healthy',
        'service': 'OCR Service',
        'version': '1.0.0'
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)